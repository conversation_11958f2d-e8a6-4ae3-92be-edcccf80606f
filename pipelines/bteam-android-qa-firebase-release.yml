trigger: none

resources:
  pipelines:
    - pipeline: qa_build
      source: bTeam Android QA Build
      trigger: true

jobs:
- job: Firebase_release
  pool:
    vmImage: 'macos-latest'

  steps:
    - checkout: none

    - download: qa_build
      patterns: |
        Android_bin/*.apk*
        Android_bin/*.aab*

    - task: NodeTool@0
      inputs:
        versionSpec: '18.x'
      displayName: 'Install Node.js'

    - powershell: |
        npm install -g firebase-tools
        firebase --version
      displayName: 'Install Firebase CLI'
    
    - task: DownloadSecureFile@1
      name: firebaseSA
      inputs:
        secureFile: 'bteam-firebasesa.json'
      displayName: 'Download Firebase Service Account Key'

    - script: |
        export GOOGLE_APPLICATION_CREDENTIALS=$(firebaseSA.secureFilePath)
        echo $GOOGLE_APPLICATION_CREDENTIALS
        echo "Distributing App to Firebase App Distribution..."
        firebase appdistribution:distribute "$(Pipeline.Workspace)/qa_build/Android_bin/app-release.apk" --app "1:************:android:bdba41b6f6e6e08159e81a" --release-notes "QA Build" --groups "qa-testers"
      displayName: 'Distribute APK via Firebase'