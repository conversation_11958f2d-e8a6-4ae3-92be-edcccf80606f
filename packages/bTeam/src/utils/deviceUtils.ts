import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import uuid from 'react-native-uuid';

const DEVICE_UUID_KEY = '@device_uuid';

/**
 * Generates a UUID once and persists it. Returns the existing UUID if already generated.
 * @returns {Promise<string>} The device UUID
 */
export const getOrCreateDeviceUUID = async (): Promise<string> => {
  try {
    // Try to get existing UUID from storage
    const existingUUID = await AsyncStorage.getItem(DEVICE_UUID_KEY);
    
    if (existingUUID) {
      return existingUUID;
    }
    
    // Generate new UUID if none exists
    const newUUID = uuid.v4() as string;
    
    // Store the new UUID
    await AsyncStorage.setItem(DEVICE_UUID_KEY, newUUID);
    
    return newUUID;
  } catch (error) {
    console.error('Error getting or creating device UUID:', error);
    // Fallback to generating a new UUID without persistence
    return uuid.v4() as string;
  }
};

/**
 * Gets the current app version
 * @returns {string} The app version
 */
export const getAppVersion = (): string => {
  try {
    return DeviceInfo.getVersion();
  } catch (error) {
    console.error('Error getting app version:', error);
    return '1.0.0'; // Fallback version
  }
};

/**
 * Gets the current app build number
 * @returns {string} The app build number
 */
export const getBuildNumber = (): string => {
  try {
    return DeviceInfo.getBuildNumber();
  } catch (error) {
    console.error('Error getting build number:', error);
    return '1'; // Fallback build number
  }
};

/**
 * Gets the device OS version
 * @returns {string} The OS version
 */
export const getOSVersion = (): string => {
  try {
    return DeviceInfo.getSystemVersion();
  } catch (error) {
    console.error('Error getting OS version:', error);
    return Platform.OS === 'ios' ? '16.0' : '7.0'; // Fallback versions
  }
};

/**
 * Gets the device OS name
 * @returns {string} The OS name
 */
export const getOSName = (): string => {
  return Platform.OS === 'ios' ? 'iOS' : 'Android';
};

/**
 * Gets the client platform type
 * @returns {number} The platform type (2 for mobile)
 */
export const getClientPlatformType = (): number => {
  return 2; // Mobile platform type
};

/**
 * Gets the client device category
 * @returns {string} The device category
 */
export const getClientDeviceCategory = (): string => {
  return 'Mobile';
};

/**
 * Gets the client device agent
 * @returns {string} The device agent
 */
export const getClientDeviceAgent = (): string => {
  return 'React Native';
};

/**
 * Builds the complete ClientPlatformInfo object for login
 * @returns {Promise<object>} The ClientPlatformInfo object
 */
export const buildClientPlatformInfo = async () => {
  const deviceUUID = await getOrCreateDeviceUUID();
  const appVersion = getAppVersion();
  const buildNumber = getBuildNumber();
  const osVersion = getOSVersion();
  const osName = getOSName();
  
  return {
    ClientPlatformType: getClientPlatformType(),
    ClientApplicationVersion: `${appVersion}(${buildNumber})`,
    ClientDeviceCategory: getClientDeviceCategory(),
    ClientDeviceOsName: osName,
    ClientDeviceOsVersion: osVersion,
    ClientDeviceAgent: getClientDeviceAgent(),
    ClientDeviceDB_Id: deviceUUID,
  };
};
