import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  getFoldersUserSettings,
  getFoldersUserSettingsSuccess,
  getFoldersUserSettingsFailed,
} from "../slices/gridMessageSlice";
import { ResponseDTO } from "../types/DTOs/ResponseDTO";
import { FoldersUserSettingsDTO } from "../types/DTOs/FoldersUserSettingsDTO";

export const foldersUserSettings = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map(
    (state) => state?.persist?.bTeamAuth?.domainBaseUrl
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === getFoldersUserSettings.type
  )
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      return {
        url: `${domainBaseUrl}/api/UserFolderSettings?calculateCounters=true`,
        category: "getFoldersUserSettings",
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        send: {}, // Empty object for the PUT request body
      };
    });

  const response$ = sources.HTTP.select("getFoldersUserSettings")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = xs.combine(response$).map(([response]) => {
    const responseBody = response?.body as ResponseDTO<
      FoldersUserSettingsDTO[]
    >;

    return getFoldersUserSettingsSuccess({ responseBody });
  });

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const foldersUserSettingsFailed = (sources) => {
  const response$ = sources.HTTP.select("getFoldersUserSettings")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getFoldersUserSettingsFailed(arr));

  return {
    ACTION: action$,
  };
};
