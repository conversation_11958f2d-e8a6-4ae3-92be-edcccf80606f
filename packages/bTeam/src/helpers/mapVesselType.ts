import { VesselTypeDTO } from '../types/DTOs/VesselTypeDTO';
import { VesselType } from '../types/vesselType';

export const mapVesselType = (vesselTypeDTO: VesselTypeDTO): VesselType => {
  return {
    VST_Guid: vesselTypeDTO.VST_Guid,
    VST_Name: vesselTypeDTO.VST_Name,
    VST_CreatedUserGuid: vesselTypeDTO.VST_CreatedUserGuid,
    VST_CreatedTimestamp: vesselTypeDTO.VST_CreatedTimestamp,
    VST_UpdatedUserGuid: vesselTypeDTO.VST_UpdatedUserGuid,
    VST_UpdatedTimestamp: vesselTypeDTO.VST_UpdatedTimestamp,
  };
};
