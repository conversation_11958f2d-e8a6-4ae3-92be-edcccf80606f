import { ContactTypeDTO } from '../types/DTOs/ContactTypeDTO';
import { ContactType } from '../types/contactType';

export const mapContactType = (contactTypeDTO: ContactTypeDTO): ContactType => {
  return {
    CTY_Guid: contactTypeDTO.CTY_Guid,
    CTY_Name: contactTypeDTO.CTY_Name,
    CTY_Abbreviation: contactTypeDTO.CTY_Abbreviation,
    CTY_CreatedUserGuid: contactTypeDTO.CTY_CreatedUserGuid,
    CTY_CreatedTimestamp: contactTypeDTO.CTY_CreatedTimestamp,
    CTY_UpdatedUserGuid: contactTypeDTO.CTY_UpdatedUserGuid,
    CTY_UpdatedTimestamp: contactTypeDTO.CTY_UpdatedTimestamp,
  };
};
