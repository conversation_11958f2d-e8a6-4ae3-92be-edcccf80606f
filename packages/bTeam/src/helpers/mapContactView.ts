import { Contact } from "../types/Contact";
import { ContactView } from "../types/contactView";
import {calculateAvatarName} from "./calculateAvatarName";

export const mapContactView = (contact: Contact): ContactView => {
  return {
    id: contact.CNT_Guid,
    avatarText: calculateAvatarName(contact.CNT_DisplayName),
    title: contact.CNT_DisplayName,
    subtitle: contact.CNT_Code || null,
    contactType: contact.CNT_Type,
  };
};
