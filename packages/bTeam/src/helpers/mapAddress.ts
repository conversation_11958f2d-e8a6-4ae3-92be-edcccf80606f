import { AddressDTO } from '../types/DTOs/AddressDTO';
import { Address } from '../types/address';

export const mapAddress = (addressDTO: AddressDTO): Address => {
  return {
    ADR_Guid: addressDTO.ADR_Guid,
    ADR_CNT_Guid: addressDTO.ADR_CNT_Guid,
    ADR_Street: addressDTO.ADR_Street,
    ADR_CIT_Guid: addressDTO.ADR_CIT_Guid,
    ADR_State: addressDTO.ADR_State,
    ADR_PostCode: addressDTO.ADR_PostCode,
    ADR_COU_Guid: addressDTO.ADR_COU_Guid,
    ADR_CreatedUserGuid: addressDTO.ADR_CreatedUserGuid,
    ADR_CreatedTimestamp: addressDTO.ADR_CreatedTimestamp,
    ADR_UpdatedUserGuid: addressDTO.ADR_UpdatedUserGuid,
    ADR_UpdatedTimestamp: addressDTO.ADR_UpdatedTimestamp,
  };
};
