import {
  SyncEntityDTO,
  MobileSyncEntityDTO,
} from "../types/DTOs/SyncEntityDTO";
import { SyncEntity } from "../types/syncEntity";

export const mapSyncEntity = (
  dto: SyncEntityDTO,
  syncEntGroupId: string
): SyncEntity => {
  const mobileSyncEntity: MobileSyncEntityDTO = dto.MobileSyncEntity;

  return {
    MSE_Guid: mobileSyncEntity.MSE_Guid,
    MSE_EntityName: mobileSyncEntity.MSE_EntityName,
    MSE_ENT_Id: mobileSyncEntity.MSE_ENT_Id,
    MSE_IsEnabled: mobileSyncEntity.MSE_IsEnabled,
    MSE_CreatedUserGuid: mobileSyncEntity.MSE_CreatedUserGuid,
    MSE_CreatedTimestamp: mobileSyncEntity.MSE_CreatedTimestamp,
    MSE_UpdatedUserGuid: mobileSyncEntity.MSE_UpdatedUserGuid,
    MSE_UpdatedTimestamp: mobileSyncEntity.MSE_UpdatedTimestamp,
    MSE_HasTimeLimitation: mobileSyncEntity.MSE_HasTimeLimitation,
    Count: dto.Count,
    SyncOrder: dto.SyncOrder,
    MUI_TableSettingsFlagChanged: dto.MUI_TableSettingsFlagChanged,
    syncEntGroupId,
  };
};
