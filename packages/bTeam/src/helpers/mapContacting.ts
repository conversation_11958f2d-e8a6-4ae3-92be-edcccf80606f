import { ContactingD<PERSON> } from '../types/DTOs/ContactingDTO';
import { Contacting } from '../types/contacting';

export const mapContacting = (contactingDTO: ContactingDTO): Contacting => {
  return {
    CTG_Guid: contactingDTO.CTG_Guid,
    CTG_CTT_Guid: contactingDTO.CTG_CTT_Guid,
    CTG_CNT_Guid: contactingDTO.CTG_CNT_Guid,
    CTG_VSL_Guid: contactingDTO.CTG_VSL_Guid,
    CTG_DisplayName: contactingDTO.CTG_DisplayName,
    CTG_Value: contactingDTO.CTG_Value,
    mailingListOfSubscribers: contactingDTO.mailingListOfSubscribers,
    CTG_CreatedUserGuid: contactingDTO.CTG_CreatedUserGuid,
    CTG_CreatedTimestamp: contactingDTO.CTG_CreatedTimestamp,
    CTG_UpdatedUserGuid: contactingDTO.CTG_UpdatedUserGuid,
    CTG_UpdatedTimestamp: contactingD<PERSON>.CTG_UpdatedTimestamp,
    CTG_CTT_Combo: contactingD<PERSON>.CTG_CTT_Combo,
    CTG_ImportedTablePK_Val: contactingDTO.CTG_ImportedTablePK_Val,
  };
};
