import { ContactDTO } from '../types/DTOs/ContactDTO';
import { Contact } from '../types/Contact';

export const mapContact = (contactDTO: ContactDTO): Contact => {
  return {
    CNT_Guid: contactDTO.CNT_Guid,
    CNT_CNT_Guid: contactDTO.CNT_CNT_Guid,
    CNT_CNT_DisplayName: contactDTO.CNT_CNT_DisplayName,
    CNT_Type: contactDTO.CNT_Type,
    CNT_Prefix: contactDTO.CNT_Prefix,
    CNT_FirstName: contactDTO.CNT_FirstName,
    CNT_MiddleName: contactDTO.CNT_MiddleName,
    CNT_LastName: contactDTO.CNT_LastName,
    CNT_DisplayName: contactDTO.CNT_DisplayName,
    CNT_ImportId: contactDTO.CNT_ImportId,
    ADR_Street: contactDTO.ADR_Street,
    ADR_COU_Guid: contactDTO.ADR_COU_Guid,
    ADR_CIT_Guid: contactDTO.ADR_CIT_Guid,
    CIT_Name: contactDTO.CIT_Name,
    ADR_State: contactDTO.ADR_State,
    ADR_PostCode: contactDTO.ADR_PostCode,
    COU_Name: contactDTO.COU_Name,
    CNT_Notes: contactDTO.CNT_Notes,
    CNT_CreatedUserGuid: contactDTO.CNT_CreatedUserGuid,
    CNT_UpdatedUserGuid: contactDTO.CNT_UpdatedUserGuid,
    Classifications: contactDTO.Classifications,
    FLN_EntityPK_Guid_Temp: contactDTO.FLN_EntityPK_Guid_Temp,
    FLN_Guid: contactDTO.FLN_Guid,
    companyGuid: contactDTO.companyGuid,
    departmentGuid: contactDTO.departmentGuid,
    CNT_UserAccessLevel: contactDTO.CNT_UserAccessLevel,
    restrictedPaticipants: contactDTO.restrictedPaticipants,
    contactingValue: contactDTO.contactingValue,
    contactingDisplayName: contactDTO.contactingDisplayName,
    CNT_ImportedTablePK_Val: contactDTO.CNT_ImportedTablePK_Val,
    CNT_MaxEmailMessageSizeMb: contactDTO.CNT_MaxEmailMessageSizeMb,
    CNT_Abbreviation: contactDTO.CNT_Abbreviation,
  };
};
