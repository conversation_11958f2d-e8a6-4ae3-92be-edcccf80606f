import { ContactingTypeD<PERSON> } from '../types/DTOs/ContactingTypeDTO';
import { ContactingType } from '../types/contactingType';

export const mapContactingType = (contactingTypeDTO: ContactingTypeDTO): ContactingType => {
  return {
    CTT_Guid: contactingTypeDTO.CTT_Guid,
    CTT_Code: contactingTypeDTO.CTT_Code,
    CTT_DisplayName: contactingTypeDTO.CTT_DisplayName,
    CTT_ValueValidationType: contactingTypeDTO.CTT_ValueValidationType,
    CTT_SortOrder: contactingTypeDTO.CTT_SortOrder,
    CTT_ContactTypeBits: contactingTypeDTO.CTT_ContactTypeBits,
    CTT_CreatedUserGuid: contactingTypeDTO.CTT_CreatedUserGuid,
    CTT_CreatedTimestamp: contactingTypeDTO.CTT_CreatedTimestamp,
    CTT_UpdatedUserGuid: contactingTypeDTO.CTT_UpdatedUserGuid,
    CTT_UpdatedTimestamp: contactingTypeDTO.CTT_UpdatedTimestamp,
  };
};
