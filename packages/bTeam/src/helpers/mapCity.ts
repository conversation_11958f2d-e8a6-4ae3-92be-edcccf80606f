import { CityDTO } from '../types/DTOs/CityDTO';
import { City } from '../types/city';

export const mapCity = (cityDTO: CityDTO): City => {
  return {
    CIT_Guid: cityDTO.CIT_Guid,
    CIT_COU_Guid: cityDTO.CIT_COU_Guid,
    CIT_Name: cityDTO.CIT_Name,
    CIT_CreatedUserGuid: cityDTO.CIT_CreatedUserGuid,
    CIT_CreatedTimestamp: cityDTO.CIT_CreatedTimestamp,
    CIT_UpdatedUserGuid: cityDTO.CIT_UpdatedUserGuid,
    CIT_UpdatedTimestamp: cityDTO.CIT_UpdatedTimestamp,
    PrimaryKeyValue: cityDTO.PrimaryKeyValue,
    CreatedUserGuid: cityDTO.CreatedUserGuid,
    UpdatedUserGuid: cityDTO.UpdatedUserGuid,
  };
};
