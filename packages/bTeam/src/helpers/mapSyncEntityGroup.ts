import { SyncEntityGroupDTO } from "../types/DTOs/SyncEntityGroupDTO";
import { SyncEntityGroup } from "../types/syncEntityGroup";

export const mapSyncEntityGroup = (
  dto: SyncEntityGroupDTO
): SyncEntityGroup => {
  return {
    MEG_Id: dto.MEG_Id,
    MEG_Guid: dto.MEG_Guid,
    MEG_Name: dto.MEG_Name,
    MEG_IsEnabled: dto.MEG_IsEnabled,
    MEG_CreatedUserGuid: dto.MEG_CreatedUserGuid,
    MEG_CreatedTimestamp: dto.MEG_CreatedTimestamp,
    MEG_UpdatedUserGuid: dto.MEG_UpdatedUserGuid,
    MEG_UpdatedTimestamp: dto.MEG_UpdatedTimestamp,
  };
};
