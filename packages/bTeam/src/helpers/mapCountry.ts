import { CountryDTO } from "../types/DTOs/CountryDTO";
import { Country } from "../types/country";

export const mapCountry = (dto: CountryDTO): Country => {
  return {
    COU_Guid: dto.COU_Guid,
    COU_Name: dto.COU_Name,
    COU_IsoCode2: dto.COU_IsoCode2,
    COU_IsoCode3: dto.COU_IsoCode3,
    COU_CreatedUserGuid: dto.COU_CreatedUserGuid,
    COU_CreatedTimestamp: dto.COU_CreatedTimestamp,
    COU_UpdatedUserGuid: dto.COU_UpdatedUserGuid,
    COU_UpdatedTimestamp: dto.COU_UpdatedTimestamp,
    PrimaryKeyValue: dto.PrimaryKeyValue,
    CreatedUserGuid: dto.CreatedUserGuid,
    UpdatedUserGuid: dto.UpdatedUserGuid,
  };
};
