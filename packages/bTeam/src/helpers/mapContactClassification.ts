import { ContactClassificationDTO } from '../types/DTOs/ContactClassificationDTO';
import { ContactClassification } from '../types/contactClassification';

export const mapContactClassification = (contactClassificationDTO: ContactClassificationDTO): ContactClassification => {
  return {
    CCL_Guid: contactClassificationDTO.CCL_Guid,
    CCL_CNT_Guid: contactClassificationDTO.CCL_CNT_Guid,
    CCL_CLA_Guid: contactClassificationDTO.CCL_CLA_Guid,
    CCL_CreatedUserGuid: contactClassificationDTO.CCL_CreatedUserGuid,
    CCL_CreatedTimestamp: contactClassificationDTO.CCL_CreatedTimestamp,
    CCL_UpdatedUserGuid: contactClassificationDTO.CCL_UpdatedUserGuid,
    CCL_UpdatedTimestamp: contactClassificationDTO.CCL_UpdatedTimestamp,
    CLA_Code: contactClassificationDTO.CLA_Code,
    CLA_Name: contactClassificationDTO.CLA_Name,
  };
};
