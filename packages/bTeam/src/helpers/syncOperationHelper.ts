import axios, { AxiosResponse } from "axios";
import {
  getSyncEntities,
  getSyncEntitiesFailed,
  getSyncEntitiesGroups,
  getSyncEntitiesGroupsFailed,
  getSyncEntitiesGroupsSuccess,
  getSyncEntitiesSuccess,
  syncContactsFinish,
  syncContactsStart,
  syncCompletion,
  syncCompletionSuccess,
  syncCompletionFailed,
  syncContactsFailed,
} from "../slices/syncSlice";
import { getOrCreateDeviceUUID } from "../utils/deviceUtils";
import {
  SYNC_CONTACTS_ENTITIES_ACTIONS,
  SYNC_CONTACTS_ENTITIES_GROUP,
} from "../constants/syncEntities";

export const makeAxiosRequest = async <T = any>(
  url: string,
  onStart: () => void,
  onSuccess: (data: T) => void,
  onFailure: (error: any) => void,
  options?: {
    method?: "GET" | "POST" | "PUT" | "DELETE";
    data?: any;
    headers?: Record<string, string>;
    timeout?: number;
  }
): Promise<T> => {
  const { method = "GET", data, headers = {}, timeout = 30000 } = options || {};

  try {
    onStart();

    const response: AxiosResponse<T> = await axios({
      url,
      method,
      data,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      timeout,
    });

    onSuccess(response.data);
    return response.data;
  } catch (error) {
    onFailure(error);
    throw error;
  }
};

export const syncOperation = async ({
  token,
  domainBaseUrl,
  userGuid,
  dispatch,
}: {
  token: string;
  domainBaseUrl: string;
  userGuid: string;
  dispatch: Function;
}): Promise<void> => {
  try {
    const dbId = await getOrCreateDeviceUUID();

    const syncEntitiesGroupResponse = await makeAxiosRequest(
      `${domainBaseUrl}/api/MobileSyncEntGroups`,
      () => {
        dispatch(getSyncEntitiesGroups());
      },
      (data) => {
        dispatch(getSyncEntitiesGroupsSuccess(data));
      },
      (error) => {
        dispatch(getSyncEntitiesGroupsFailed(error));
      },
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const syncEntGroupId = syncEntitiesGroupResponse.find(
      (group: any) => group.MEG_Id === SYNC_CONTACTS_ENTITIES_GROUP.contactsSync
    )?.MEG_Guid;

    const syncEntities = await makeAxiosRequest(
      `${domainBaseUrl}/api/MobileUserSyncDatabases?userGuid=${userGuid}&dbId=${dbId}&syncEntGroup=${syncEntGroupId}`,
      () => {
        dispatch(getSyncEntities());
      },
      (data) => {
        dispatch(
          getSyncEntitiesSuccess({ responseBody: data, syncEntGroupId })
        );
      },
      (error) => {
        dispatch(getSyncEntitiesFailed(error));
      },
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    // CONTACTS SYNC START

    const PAGE_SIZE = 1000;

    const allRequests: Promise<any>[] = [];

    dispatch(syncContactsStart());

    for (const entityInfo of syncEntities) {
      const entityName = entityInfo.MobileSyncEntity.MSE_EntityName;
      const entityGuid = entityInfo.MobileSyncEntity.MSE_Guid;
      const entityCount = entityInfo.Count;
      const actions = SYNC_CONTACTS_ENTITIES_ACTIONS[entityName];
      const totalPages = Math.ceil(entityCount / PAGE_SIZE);

      // Skip if we don't have actions for this entity or if count is zero
      if (!SYNC_CONTACTS_ENTITIES_ACTIONS[entityName] || entityCount === 0) {
        continue;
      }

      // Create requests for all pages of this entity
      for (let page = 1; page <= totalPages; page++) {
        const request = makeAxiosRequest(
          `${domainBaseUrl}/api/MobileUserSyncDatabases?userGuid=${userGuid}&dbId=${dbId}&entityGuid=${entityGuid}&entityName=${entityName}&page=${page}&pageSize=${PAGE_SIZE}`,
          () => {
            if (page === 1) {
              dispatch(actions.getAction());
            }
          },
          (data) => {
            dispatch(
              actions.getSuccessAction({
                data,
                isLastPage: page === totalPages,
              })
            );
          },
          (error) => {
            dispatch(actions.getFailedAction(error));
          },
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        allRequests.push(request);
      }
    }

    try {
      await Promise.all(allRequests);
      dispatch(syncContactsFinish());
    } catch (error) {
      dispatch(syncContactsFailed(error?.message || "Sync contacts failed"));
      return; // Exit early, don't proceed to sync completion request
    }

    // Sync completion
    await makeAxiosRequest(
      `${domainBaseUrl}/api/MobileUserSyncDatabases?userGuid=${userGuid}&dbId=${dbId}&syncEntGroup=${syncEntGroupId}`,
      () => dispatch(syncCompletion()),
      (data) => dispatch(syncCompletionSuccess(data)),
      (error) => dispatch(syncCompletionFailed(error)),
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    // CONTACTS SYNC END
  } catch (error) {
    console.error("Error in syncOperation:", error);
  }
};
