export type MobileSyncEntityDTO = {
  MSE_Guid: string;
  MSE_EntityName: string;
  MSE_ENT_Id: number;
  MSE_IsEnabled: boolean;
  MSE_CreatedUserGuid: string;
  MSE_CreatedTimestamp: string;
  MSE_UpdatedUserGuid: string;
  MSE_UpdatedTimestamp: string;
  MSE_HasTimeLimitation: boolean;
};

export type SyncEntityDTO = {
  MobileSyncEntity: MobileSyncEntityDTO;
  Count: number;
  SyncOrder: number;
  MUI_TableSettingsFlagChanged: null | any;
};
