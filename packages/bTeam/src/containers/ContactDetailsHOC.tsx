import React from "react";
import { useSelector } from "react-redux";
import { useRoute, useNavigation } from "@react-navigation/native";
import {
  getDepartmentsByCompany,
  getPeopleByCompany,
  getVesselsByCompany,
} from "../components/contacts/helpers/contactFilters";
import { Contact } from "../types/Contact";
import { mapContactView } from "../helpers/mapContactView";
import { SCREEN_NAMES } from "../constants/screenNames";

// Components
import GeneralContactScreen from "../components/contacts/GeneralContactScreen";

const ContactDetailsHOC: React.FC = () => {
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const route = useRoute();
  const navigation = useNavigation();
  const contactGuid = (route.params as any)?.contactGuid;

  const departmentsList = getDepartmentsByCompany(contacts, contactGuid)
    .map((id: string) => contacts.byId[id])
    .filter(Boolean)
    .map((contact: Contact) => mapContactView(contact));
  const peopleList = getPeopleByCompany(contacts, contactGuid)
    .map((id: string) => contacts.byId[id])
    .filter(Boolean)
    .map((contact: Contact) => mapContactView(contact));
  const vesselsList = getVesselsByCompany(contacts, contactGuid)
    .map((id: string) => contacts.byId[id])
    .filter(Boolean)
    .map((contact: Contact) => mapContactView(contact));

  const navigateToScreen = (contactId: string) => {
    const contact = contacts.byId[contactId];
    if (!contact) return;

    const navDictionary = {
      1: SCREEN_NAMES.generalContactDetails,
      2: SCREEN_NAMES.contactDetails,
      4: SCREEN_NAMES.vesselDetails,
      8: SCREEN_NAMES.departmentDetails,
    };

    navigation.navigate(navDictionary[contact.CNT_Type], {
      contactGuid: contactId,
    });
  };

  return (
    <GeneralContactScreen
      departmentsList={departmentsList}
      peopleList={peopleList}
      vesselsList={vesselsList}
      navigateToScreen={navigateToScreen}
      companyGuid={contactGuid}
    />
  );
};

export default ContactDetailsHOC;
