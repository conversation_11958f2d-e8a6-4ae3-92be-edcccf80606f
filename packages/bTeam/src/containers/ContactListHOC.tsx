import React from "react";
import ContactListScreen from "../components/contactList/ContactListScreen";
import { useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { Contact } from "../types/Contact";
import { ContactView } from "../types/contactView";
import { mapContactView } from "../helpers/mapContactView";
import { SCREEN_NAMES } from "../constants/screenNames";

const ContactListHOC: React.FC = () => {
  const {
    byId,
    companyIds,
    personIds,
    vesselIds,
    departmentIds,
    branchCompanyIds,
  } = useSelector((state: any) => state.persist.bTeamContactsSlice.contacts);
  const navigation = useNavigation();
  // Aggregate all contact IDs from different type arrays
  const allContactIds = [
    ...companyIds,
    ...personIds,
    ...vesselIds,
    ...departmentIds,
    ...branchCompanyIds,
  ];

  const contactsData: ContactView[] = allContactIds
    .map((id: string) => byId[id])
    .filter(Boolean)
    .map((contact: Contact) => mapContactView(contact));

  const handleTapContact = (contactId: string) => {
    const navDictionary = {
      1: SCREEN_NAMES.generalContactDetails,
      2: SCREEN_NAMES.contactDetails,
      4: SCREEN_NAMES.vesselDetails,
      8: SCREEN_NAMES.departmentDetails,
    };
    const contactType = byId[contactId]?.CNT_Type;

    return navigation.navigate(navDictionary[contactType], {
      contactGuid: contactId,
    });
  };

  return (
    <ContactListScreen
      contactsData={contactsData}
      listTitle={"Contacts"}
      emptyListMessage={"No contacts found"}
      isLoading={false}
      handleRefreshList={() => {}}
      handleTapContact={handleTapContact}
    />
  );
};

export default ContactListHOC;
