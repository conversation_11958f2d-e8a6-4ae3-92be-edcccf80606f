import {
  getAddresses,
  getAddressesFailed,
  getAddressesSuccess,
  getCities,
  getCitiesFailed,
  getCitiesSuccess,
  getClassifications,
  getClassificationsFailed,
  getClassificationsSuccess,
  getContactClassifications,
  getContactClassificationsFailed,
  getContactClassificationsSuccess,
  getC<PERSON>acting,
  getContactingFailed,
  getContactingSuc<PERSON>,
  getContactingTypes,
  getContactingTypesFailed,
  getContactingTypesSuccess,
  getContacts,
  getContactsFailed,
  getContactsSuccess,
  getContactTypes,
  getContactTypesFailed,
  getContactTypesSuccess,
  getCountries,
  getCountriesFailed,
  getCountriesSuccess,
  getVesselTypes,
  getVesselTypesFailed,
  getVesselTypesSuccess,
} from "../slices/contactsSlice";

export const SYNC_CONTACTS_ENTITIES_GROUP = {
  contactsSync: 1000,
  messagesSync: 2000,
  configurationSync: 3000,
  communicationSync: 4000,
};

export const SYNC_CONTACTS_ENTITIES_NAMES = {
  country: "Country",
  city: "City",
  classification: "Classification",
  contactType: "ContactType",
  contactingType: "ContactingType",
  vesselType: "VesselType",
  contact: "Contact",
  address: "Address",
  contactClassification: "ContactClassification",
  contacting: "Contacting",
};

export const CONTACT_TYPES = {
  company: 1,
  person: 2,
  vessel: 4,
  department: 8,
  branchCompany: 16,
} as const;

// Create a mapping of entity names to their corresponding actions
export const SYNC_CONTACTS_ENTITIES_ACTIONS: Record<
  string,
  {
    getAction: () => any;
    getSuccessAction: (payload: { data: any; isLastPage?: boolean }) => any;
    getFailedAction: (error: any) => any;
  }
> = {
  [SYNC_CONTACTS_ENTITIES_NAMES.country]: {
    getAction: () => getCountries(),
    getSuccessAction: (payload) => getCountriesSuccess(payload),
    getFailedAction: (error) => getCountriesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.city]: {
    getAction: () => getCities(),
    getSuccessAction: (payload) => getCitiesSuccess(payload),
    getFailedAction: (error) => getCitiesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.classification]: {
    getAction: () => getClassifications(),
    getSuccessAction: (payload) => getClassificationsSuccess(payload),
    getFailedAction: (error) => getClassificationsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactType]: {
    getAction: () => getContactTypes(),
    getSuccessAction: (payload) => getContactTypesSuccess(payload),
    getFailedAction: (error) => getContactTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactingType]: {
    getAction: () => getContactingTypes(),
    getSuccessAction: (payload) => getContactingTypesSuccess(payload),
    getFailedAction: (error) => getContactingTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.vesselType]: {
    getAction: () => getVesselTypes(),
    getSuccessAction: (payload) => getVesselTypesSuccess(payload),
    getFailedAction: (error) => getVesselTypesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contact]: {
    getAction: () => getContacts(),
    getSuccessAction: (payload) => getContactsSuccess(payload),
    getFailedAction: (error) => getContactsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.address]: {
    getAction: () => getAddresses(),
    getSuccessAction: (payload) => getAddressesSuccess(payload),
    getFailedAction: (error) => getAddressesFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contactClassification]: {
    getAction: () => getContactClassifications(),
    getSuccessAction: (payload) => getContactClassificationsSuccess(payload),
    getFailedAction: (error) => getContactClassificationsFailed(error),
  },
  [SYNC_CONTACTS_ENTITIES_NAMES.contacting]: {
    getAction: () => getContacting(),
    getSuccessAction: (payload) => getContactingSuccess(payload),
    getFailedAction: (error) => getContactingFailed(error),
  },
};
