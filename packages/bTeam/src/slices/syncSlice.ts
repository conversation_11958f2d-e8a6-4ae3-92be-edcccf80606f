import { createSlice } from "@reduxjs/toolkit";
import { SyncEntityGroup } from "../types/syncEntityGroup";
import { SyncEntity } from "../types/syncEntity";
import { SyncEntityDTO } from "../types/DTOs/SyncEntityDTO";
import { SyncEntityGroupDTO } from "../types/DTOs/SyncEntityGroupDTO";
import { mapSyncEntityGroup } from "../helpers/mapSyncEntityGroup";
import { mapSyncEntity } from "../helpers/mapSyncEntity";

// Initial state
const initialState = {
  syncEntitiesGroups: {
    byId: {} as Record<string, SyncEntityGroup>,
    allIds: [] as string[],
  },
  syncEntitiesGroupsIsLoading: false as boolean,
  syncEntitiesGroupsErrorText: null as string | null,

  syncEntities: {
    byId: {} as Record<string, SyncEntity>,
    allIds: [] as string[],
  },
  syncEntitiesIsLoading: false as boolean,
  syncEntitiesErrorText: null as string | null,

  syncContactsIsLoading: false as boolean,
  syncContactsErrorText: null as string | null,

  syncCompletionIsLoading: false as boolean,
  syncCompletionErrorText: null as string | null,
  syncContactsDate: null as string | null,
};

const syncSlice = createSlice({
  name: "bTeamSyncSlice",
  initialState,
  reducers: {
    getSyncEntitiesGroups: (state) => {
      state.syncEntitiesGroupsIsLoading = true;
      state.syncEntitiesGroupsErrorText = null;
    },
    getSyncEntitiesGroupsSuccess: (state, { payload }) => {
      const groups: SyncEntityGroupDTO[] = payload;
      const syncEntitiesGroupsById: Record<string, SyncEntityGroup> = {};
      const allIds: string[] = [];

      groups.forEach((group) => {
        const mappedGroup = mapSyncEntityGroup(group);
        const id = mappedGroup.MEG_Guid;
        syncEntitiesGroupsById[id] = mappedGroup;
        allIds.push(id);
      });

      state.syncEntitiesGroups.byId = syncEntitiesGroupsById;
      state.syncEntitiesGroups.allIds = allIds;
      state.syncEntitiesGroupsIsLoading = false;
      state.syncEntitiesGroupsErrorText = null;
    },
    getSyncEntitiesGroupsFailed: (state, { payload }) => {
      state.syncEntitiesGroupsIsLoading = false;
      state.syncEntitiesGroupsErrorText = payload;
    },
    getSyncEntities: (state) => {
      state.syncEntitiesIsLoading = true;
      state.syncEntitiesErrorText = null;
    },
    getSyncEntitiesSuccess: (state, { payload }) => {
      const {
        responseBody,
        syncEntGroupId,
      }: { responseBody: SyncEntityDTO[]; syncEntGroupId: string } = payload;
      const syncEntitiesById: Record<string, SyncEntity> = {};
      const allIds: string[] = [];

      responseBody.forEach((entity) => {
        const mappedEntity = mapSyncEntity(entity, syncEntGroupId);
        const id = mappedEntity.MSE_Guid;
        syncEntitiesById[id] = mappedEntity;
        allIds.push(id);
      });

      state.syncEntities.byId = syncEntitiesById;
      state.syncEntities.allIds = allIds;
      state.syncEntitiesIsLoading = false;
      state.syncEntitiesErrorText = null;
    },
    getSyncEntitiesFailed: (state, { payload }) => {
      state.syncEntitiesIsLoading = false;
      state.syncEntitiesErrorText = payload;
    },
    syncContactsStart: (state) => {
      state.syncContactsIsLoading = true;
      state.syncContactsErrorText = null;
    },
    syncContactsFinish: (state) => {
      state.syncContactsIsLoading = false;
      state.syncContactsErrorText = null;
    },
    syncContactsFailed: (state, { payload }) => {
      state.syncContactsIsLoading = false;
      state.syncContactsErrorText = payload;
    },
    syncCompletion: (state) => {
      state.syncCompletionIsLoading = true;
      state.syncCompletionErrorText = null;
    },
    syncCompletionSuccess: (state, { payload }) => {
      state.syncCompletionIsLoading = false;
      state.syncCompletionErrorText = null;
      state.syncContactsDate = payload;
    },
    syncCompletionFailed: (state, { payload }) => {
      state.syncCompletionIsLoading = false;
      state.syncCompletionErrorText = payload;
    },
  },
});

export const {
  getSyncEntitiesGroups,
  getSyncEntitiesGroupsSuccess,
  getSyncEntitiesGroupsFailed,
  getSyncEntities,
  getSyncEntitiesSuccess,
  getSyncEntitiesFailed,
  syncContactsStart,
  syncContactsFinish,
  syncContactsFailed,
  syncCompletion,
  syncCompletionSuccess,
  syncCompletionFailed,
} = syncSlice.actions;

export default syncSlice.reducer;
