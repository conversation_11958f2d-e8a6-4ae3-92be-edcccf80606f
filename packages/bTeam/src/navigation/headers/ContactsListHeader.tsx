import React from "react";
import { StyleSheet, View } from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  SPACING,
  CustomText,
  useThemeAwareObject,
  FONT_SIZES,
  FONT_WEIGHTS,
} from "b-ui-lib";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  title: string;
};

const ContactsListHeader = ({ navigation, title }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <CustomText style={styles.title}>{title}</CustomText>
      </View>
    </View>
  );
};

export default ContactsListHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    titleContainer: {
      padding: SPACING.S,
      alignItems: "center",
    },
    title: {
      padding: SPACING.S,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
  });

  return { styles };
};
