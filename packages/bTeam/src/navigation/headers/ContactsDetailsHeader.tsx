import React from "react";
import { StyleSheet, View } from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  SPACING,
  CustomText,
  useThemeAwareObject,
  FONT_SIZES,
  FONT_WEIGHTS,
  IconButton,
} from "b-ui-lib";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  title: string;
  back: boolean;
};

const ContactsDetailsHeader = ({ navigation, title, back }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      {back && (
        <IconButton
          name="chevron-left"
          size={24}
          color={color.HALF_DIMMED}
          onPress={() => navigation.goBack()}
          containerStyle={styles.backIcon}
        />
      )}

      <View style={styles.titleContainer}>
        <CustomText style={styles.title}>{title}</CustomText>
      </View>

      <View style={{ width: 24 }} />
    </View>
  );
};

export default ContactsDetailsHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
      padding: SPACING.S,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    titleContainer: {
      alignItems: "center",
    },
    title: {
      padding: SPACING.S,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
    backIcon: {},
  });

  return { styles, color };
};
