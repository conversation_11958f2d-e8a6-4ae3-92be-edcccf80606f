import { Pressable, View, StyleSheet } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  IconButton,
  SPACING,
} from "b-ui-lib";

type Props = {
  contactsList: string[];
  onPress: () => void;
};

const RenderContacts = ({ contactsList, onPress }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const onLayout = (event) => {
    const { width, height } = event.nativeEvent.layout;
    console.log("Width: ", width, "Height: ", height);
  };

  return (
    <View style={{ backgroundColor: "red", flex: 1 }} onLayout={onLayout}>
      <Pressable style={styles.contactsRow} onPress={onPress}>
        <View style={styles.contactsInfo}>
          <CustomText>{contactsList.length}</CustomText>
          <IconButton name="user" size={16} color={color.TEXT_DEFAULT} />
        </View>

        <View>
          <IconButton
            name="chevron-right"
            size={16}
            color={color.TEXT_DEFAULT}
          />
        </View>
      </Pressable>
    </View>
  );
};

export default RenderContacts;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    contactsRow: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    contactsInfo: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.XS,
    },
  });

  return { styles, color };
};
