import { useSelector } from "react-redux";
import { ScrollView, StyleSheet } from "react-native";
import { type Theme, useThemeAwareObject, CustomText, SPACING } from "b-ui-lib";
import { useRoute, useNavigation } from "@react-navigation/native";

import DetailRow from "./detailRow";
import {
  resolveCompanyName,
  resolveAddressesWithLocation,
} from "./helpers/contactResolvers";
import {
  getPhoneNumbers,
  getEmails,
  getFaxNumbers,
  getRelatedContacts,
} from "./helpers/contactFilters";
import RenderContacts from "./components/RenderContacts";
import { SCREEN_NAMES } from "../../constants/screenNames";
import { useEffect } from "react";

type Props = {
  onSubmit: (formData: { username: string; password: string }) => void;
  errorMessage?: string;
  clearLogInErrorMessageAction: () => void;
};

const DepartmentDetailsScreen = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const route = useRoute();
  const navigation = useNavigation();
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const contacting = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacting
  );
  const addresses = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.addresses
  );
  const countries = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.countries
  );
  const cities = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.cities
  );
  const contactingTypes = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contactingTypes
  );
  const contactGuid = route.params?.contactGuid;
  const contact = contacts.byId[contactGuid];

  const phoneNumbers = getPhoneNumbers(
    contacting,
    contactingTypes,
    contact.CNT_Guid
  );
  const emails = getEmails(contacting, contactingTypes, contact.CNT_Guid);
  const faxNumbers = getFaxNumbers(
    contacting,
    contactingTypes,
    contact.CNT_Guid
  );
  const contactsList = getRelatedContacts(contacts, contact.CNT_Guid).map(
    (id: string) => contacts.byId[id]
  );

  useEffect(() => {
    navigation.setOptions({
      title: contact.CNT_DisplayName,
    });
  }, []);

  const navigateToContacts = () => {
    navigation.navigate(SCREEN_NAMES.contactsList, {
      contactGuids: contactsList.map((contact: Contact) => contact.CNT_Guid),
    });
  };

  return (
    <ScrollView style={styles.container}>
      <CustomText style={styles.headerText}>Department Info</CustomText>

      <DetailRow label="Dept Name:" value={contact.CNT_DisplayName} isBold />
      <DetailRow
        label="Contacts:"
        value={
          <RenderContacts
            contactsList={contactsList}
            onPress={navigateToContacts}
          />
        }
      />
      <DetailRow
        label="Adress:"
        value={resolveAddressesWithLocation(
          contact,
          addresses,
          countries,
          cities
        ).toString()}
      />
      <DetailRow
        label="Company:"
        value={resolveCompanyName(contact, contacts)}
        isBold
      />
      <DetailRow
        label="Classifications"
        value={contact.Classifications.map((c: any) => c.CLA_Name)}
      />
      <DetailRow
        label="Phone:"
        value={phoneNumbers.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
        isPhone
      />
      <DetailRow
        label="Fax:"
        value={faxNumbers.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Tlx:" value={""} isBold />
      <DetailRow
        label="Email:"
        value={emails.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
        isMail
      />
      <DetailRow label="Notes:" value={contact.CNT_Notes} />
    </ScrollView>
  );
};

export default DepartmentDetailsScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      padding: SPACING.M,
    },
    headerText: {
      fontWeight: "700",
      marginBottom: SPACING.M,
    },
    avatar: {
      width: 65,
      height: 65,
      borderRadius: 50,
    },
  });

  return { styles };
};
