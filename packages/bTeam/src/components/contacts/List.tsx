import { useNavigation } from "@react-navigation/native";
import { FlatList, Text, View } from "react-native";

import { SCREEN_NAMES } from "../../constants/screenNames";
import ContactItem from "../contactList/ContactItem";
import { ContactView } from "../../types/contactView";
import { useThemeAwareObject } from "b-ui-lib";

type Props = {
  data: ContactView[];
};

const EmptyList = () => {
  return (
    <View>
      <Text>No data</Text>
    </View>
  );
};

const List = ({ data }: Props) => {
  const { color } = useThemeAwareObject((color) => color);
  const navigation = useNavigation();
  const contactType = data.map(
    (contact: ContactView) => contact.contactType
  )?.[0];

  const navigateToScreen = (contactGuid: string) => {
    const navDictionary = {
      2: SCREEN_NAMES.contactDetails,
      4: SCREEN_NAMES.vesselDetails,
      8: SCREEN_NAMES.departmentDetails,
    };

    navigation.navigate(navDictionary[contactType], {
      contactGuid,
    });
  };

  return (
    <FlatList
      style={{ backgroundColor: color.MESSAGE_ITEM__BACKGROUND }}
      data={data}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <ContactItem contact={item} onPress={() => navigateToScreen(item.id)} />
      )}
      ListEmptyComponent={EmptyList}
    />
  );
};

export default List;
