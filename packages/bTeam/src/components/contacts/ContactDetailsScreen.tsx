import { ScrollView, View, StyleSheet } from "react-native";
import { useRoute } from "@react-navigation/native";
import { useSelector } from "react-redux";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Avatar,
  SPACING,
} from "b-ui-lib";

import {
  resolveDepartmentName,
  resolveCompanyName,
  resolveAddresses,
} from "./helpers/contactResolvers";
import {
  getPhoneNumbers,
  getEmails,
  getFaxNumbers,
} from "./helpers/contactFilters";
import { Contact } from "../../types/Contact";

// Components
import DetailRow from "./detailRow";
import { calculateAvatarName } from "../../helpers/calculateAvatarName";

type Props = {};

const ContactDetailsScreen = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const route: any = useRoute();
  const contactGuid = route.params?.contactGuid;
  const contacting = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacting
  );
  const contactingTypes = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contactingTypes
  );
  const addresses = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.addresses
  );
  const contact: Contact = contacts.byId[contactGuid];

  const phoneNumbers = getPhoneNumbers(
    contacting,
    contactingTypes,
    contact.CNT_Guid
  );
  const emails = getEmails(contacting, contactingTypes, contact.CNT_Guid);
  const faxNumbers = getFaxNumbers(
    contacting,
    contactingTypes,
    contact.CNT_Guid
  );

  return (
    <ScrollView style={styles.container}>
      <CustomText style={styles.headerText}>Contact Info</CustomText>

      <View style={{ alignItems: "center", marginBottom: SPACING.M }}>
        <Avatar
          name={calculateAvatarName(contact?.CNT_DisplayName)}
          style={{ container: styles.avatar }}
        />
      </View>

      <DetailRow label="Name:" value={contact.CNT_DisplayName} isBold />
      <DetailRow label="Title:" value={contact.CNT_Prefix} />
      <DetailRow
        label="Company:"
        value={resolveCompanyName(contact, contacts)}
        isBold
      />
      <DetailRow
        label="Dept:"
        value={resolveDepartmentName(contact, contacts)}
        isBold
      />
      <DetailRow
        label="Adress:"
        value={resolveAddresses(contact, addresses)
          .map((a) => a.ADR_Street)
          .join(", ")}
      />
      <DetailRow
        label="Classifications"
        value={contact.Classifications.map((c) => c.CLA_Name)}
      />
      <DetailRow
        label="Phone:"
        value={phoneNumbers.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
        isPhone
      />
      <DetailRow
        label="Fax:"
        value={faxNumbers.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
      />
      <DetailRow label="Tlx:" value={""} isBold />
      <DetailRow
        label="Email:"
        value={emails.map((id: string) => contacting.byId[id]?.CTG_Value)}
        isBold
        isMail
      />
      <DetailRow label="Notes:" value={contact.CNT_Notes} />
    </ScrollView>
  );
};

export default ContactDetailsScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      padding: SPACING.M,
    },
    headerText: {
      fontWeight: "700",
      marginBottom: SPACING.M,
    },
    avatar: {
      width: 65,
      height: 65,
      borderRadius: 50,
    },
  });

  return { styles };
};
