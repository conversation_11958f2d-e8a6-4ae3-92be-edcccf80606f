export const filterContactsByType = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string,
  validationType: string
) => {
  const contactContacting = contacting.allIds.filter(
    (id: string) => contacting.byId[id]?.CTG_CNT_Guid === contactGuid
  );

  return contactContacting.filter(
    (id: string) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]
        ?.CTT_ValueValidationType === validationType
  );
};

export const filterContactsByCode = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string,
  code: string
) => {
  const contactContacting = contacting.allIds.filter(
    (id: string) => contacting.byId[id]?.CTG_CNT_Guid === contactGuid
  );

  return contactContacting.filter(
    (id: string) =>
      contactingTypes.byId[contacting.byId[id]?.CTG_CTT_Guid]?.CTT_Code === code
  );
};

export const getPhoneNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "phone");
};

export const getEmails = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "email");
};

export const getFaxNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByType(contacting, contactingTypes, contactGuid, "fax");
};

export const getTelexNumbers = (
  contacting: any,
  contactingTypes: any,
  contactGuid: string
) => {
  return filterContactsByCode(contacting, contactingTypes, contactGuid, "telex");
};

export const getRelatedContacts = (
  contacts: any,
  parentContactGuid: string
) => {
  return contacts.allIds.filter(
    (id: string) => contacts.byId[id]?.CNT_CNT_Guid === parentContactGuid
  );
};

export const getContactsByCompany = (
  contacts: any,
  companyGuid: string,
  contactType: number
) => {
  return contacts.allIds.filter((id: string) => {
    const contact = contacts.byId[id];
    if (!contact) return false;

    // Direct children of the company
    if (contact.CNT_CNT_Guid === companyGuid && contact.CNT_Type === contactType) {
      return true;
    }

    // For people, also check if they belong to departments of this company
    if (contactType === 2) { // Contact type
      const parent = contact.CNT_CNT_Guid ? contacts.byId[contact.CNT_CNT_Guid] : null;
      if (parent && parent.CNT_Type === 8 && parent.CNT_CNT_Guid === companyGuid) { // Department type
        return true;
      }
    }

    return false;
  });
};

export const getDepartmentsByCompany = (contacts: any, companyGuid: string) => {
  return getContactsByCompany(contacts, companyGuid, 8); // Department type
};

export const getPeopleByCompany = (contacts: any, companyGuid: string) => {
  return getContactsByCompany(contacts, companyGuid, 2); // Contact type
};

export const getVesselsByCompany = (contacts: any, companyGuid: string) => {
  return getContactsByCompany(contacts, companyGuid, 4); // Vessel type
};
