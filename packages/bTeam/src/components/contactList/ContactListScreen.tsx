import React from "react";
import { StyleSheet, View } from "react-native";
import { type Theme, useThemeAwareObject } from "b-ui-lib";
import ContactList from "./ContactList";
import { ContactView } from "../../types/contactView";

type Props = {
  contactsData: ContactView[];
  listTitle: string;
  emptyListMessage: string;
  isLoading: boolean;
  handleRefreshList: () => void;
  handleTapContact: (contactId: string) => void;
};

const ContactListScreen: React.FC<Props> = ({
  contactsData,
  listTitle,
  emptyListMessage,
  isLoading,
  handleRefreshList,
  handleTapContact,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <ContactList
        data={contactsData}
        title={listTitle}
        emptyListMessage={emptyListMessage}
        isLoading={isLoading}
        handleRefreshList={handleRefreshList}
        handleTapContact={handleTapContact}
      />
    </View>
  );
};

export default ContactListScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
  });

  return { styles, color };
};
