import React from "react";
import { FlatList, RefreshControl, StyleSheet, View } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
} from "b-ui-lib";
import { ContactView } from "../../types/contactView";
import ContactItem from "./ContactItem";
import Animated from "react-native-reanimated";

const AnimatedFlatList = Animated.createAnimatedComponent(
  FlatList<ContactView>
);

type Props = {
  data: ContactView[];
  title?: string;
  emptyListMessage?: string;
  isLoading?: boolean;
  handleRefreshList: () => void;
  handleTapContact: (contactId: string) => void;
};

const ContactList = ({
  data,
  title,
  emptyListMessage,
  isLoading,
  handleRefreshList,
  handleTapContact,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      {title && <CustomText style={styles.title}>{title}</CustomText>}
      <AnimatedFlatList
        data={data}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={() => handleRefreshList()}
            tintColor={color.BLACK}
          />
        }
        keyExtractor={(item: ContactView) => item.id}
        renderItem={({ item }) => {
          return <ContactItem contact={item} onPress={handleTapContact} />;
        }}
        ListEmptyComponent={
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              paddingTop: 70,
            }}
          >
            <CustomText>
              {emptyListMessage ? emptyListMessage : "No Contacts"}
            </CustomText>
          </View>
        }
      />
    </View>
  );
};

export default ContactList;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    title: {
      padding: SPACING.M,
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.TEXT_SEARCH_INVERTED,
    },
  });

  return { styles, color };
};
