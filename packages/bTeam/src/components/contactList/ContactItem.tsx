import React from "react";
import { StyleSheet, View, Pressable } from "react-native";
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  BenefitIconSet,
  SPACING,
  FONT_SIZES,
  Avatar,
} from "b-ui-lib";
import { CONTACT_TYPES } from "../../constants/syncEntities";
import { ContactView } from "../../types/contactView";

type Props = {
  contact: ContactView;
  onPress?: (id: string) => void;
};

const ContactItem: React.FC<Props> = ({ contact, onPress }) => {
  const { id, avatarText, title, subtitle, contactType } = contact;
  const { styles, color } = useThemeAwareObject(createStyles);

  const CONTACT_ICONS = {
    [CONTACT_TYPES.company]: "anchor",
    [CONTACT_TYPES.vessel]: "cargo-ship",
    //TODO change department icon
    [CONTACT_TYPES.department]: "users",
  };

  const handlePress = () => onPress && onPress(id);

  return (
    <Pressable style={styles.container} onPress={handlePress}>
      <View style={styles.iconContainer}>
        {contactType === CONTACT_TYPES.person ? (
          <Avatar name={avatarText} />
        ) : (
          <BenefitIconSet
            name={CONTACT_ICONS[contactType]}
            size={26}
            color={color.EMAIL_ICON}
          />
        )}
      </View>
      <View style={styles.textContainer}>
        <CustomText style={styles.title} numberOfLines={2}>
          {title}
        </CustomText>
        {subtitle && (
          <CustomText style={styles.subtitle} numberOfLines={1}>
            {subtitle}
          </CustomText>
        )}
      </View>
    </Pressable>
  );
};

export default ContactItem;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.M,
      paddingHorizontal: SPACING.TWENTY_TWO,
      paddingVertical: SPACING.EIGHTEEN,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_EMAIL_FOLDER,
    },
    iconContainer: {
      flex: 1,
      alignItems: "center",
    },
    textContainer: {
      flex: 9,
      gap: SPACING.XXS,
    },
    title: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "700",
      color: color.TEXT_SEARCH_INVERTED,
    },
    subtitle: {
      fontSize: FONT_SIZES.TEN,
      // TODO change color to ui-lib 797986 - 999999
      color: color.TEXT_SECONDARY,
    },
  });

  return { styles, color };
};
