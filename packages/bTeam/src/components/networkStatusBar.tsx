import { useEffect, useRef, useState } from "react";
import { View, Animated } from "react-native";
import NetInfo from "@react-native-community/netinfo";
import { useDispatch, useSelector } from "react-redux";

// Components
import { CustomText, useThemeAwareObject, type Theme } from "b-ui-lib";

// Actions
import {
  setCurrentNetwork,
  setIsConnectedToNetwork,
} from "../slices/generalSlice";

type Props = {};

const NetworkStatusBar = ({}: Props) => {
  const dispatch = useDispatch();
  const { color } = useThemeAwareObject((theme: Theme) => theme);
  const { isConnectedToNetwork } = useSelector(
    (state: any) => state.root.bTeamGeneralSlice
  );

  const heightAnim = useRef(new Animated.Value(0)).current;
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [showNetworkStatus, setShowNetworkStatus] = useState(false);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      dispatch(setIsConnectedToNetwork(state.isConnected));
      dispatch(setCurrentNetwork(state.type));
      
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      setShowNetworkStatus(true);

      // Animate the container to show
      Animated.timing(heightAnim, {
        toValue: 20,
        duration: 500,
        useNativeDriver: false,
      }).start();

      // Only hide after 3 seconds if connected (online)
      // If offline, keep it visible
      if (state.isConnected) {
        timeoutRef.current = setTimeout(() => {
          Animated.timing(heightAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: false,
          }).start();
          setShowNetworkStatus(false);
        }, 3000);
      }
    });

    return () => {
      unsubscribe();
      // Clean up timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <View>
      {showNetworkStatus && (
        <Animated.View
          style={{
            height: heightAnim,
            backgroundColor: isConnectedToNetwork
              ? color.SUCCESS
              : color.ERROR,
            justifyContent: "center",
          }}
        >
          <CustomText
            style={{
              textAlign: "center",
              fontSize: 10,
              color: color.TEXT_DEFAULT,
            }}
          >
            {isConnectedToNetwork
              ? "You are online."
              : "You are currently offline..."}
          </CustomText>
        </Animated.View>
      )}
    </View>
  );
};

export default NetworkStatusBar;