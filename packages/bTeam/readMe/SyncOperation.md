# Contacts Sync Operation System

## Overview

The Contacts Sync Operation is a comprehensive data synchronization system designed to efficiently sync contact-related entities from the server to the mobile application. The system supports **dynamic entity processing**, **dynamic pagination**, **parallel request execution**, and **robust error handling**.

## Architecture

### Core Components

1. **`syncOperationHelper.ts`** - Main orchestration logic
2. **`syncSlice.ts`** - Global sync state management  
3. **`contactsSlice.ts`** - Individual entity state management
4. **`syncEntities.ts`** - Constants and action mappings

## System Flow

### 1. Initial Setup Phase
```
📡 GET /api/MobileUserSyncEntitiesGroups
    ↓
🔍 Find contactsSync group (ID: 1000)
    ↓
📡 GET /api/MobileUserSyncEntities (with group GUID)
    ↓
📋 Receive list of entities with counts
```

### 2. Dynamic Entity Sync Phase
```
🚀 dispatch(syncContactsStart())
    ↓
🔄 Loop through each entity from API response
    ↓
📊 Calculate pagination (totalPages = Math.ceil(count / PAGE_SIZE))
    ↓
⚡ Create ALL requests for ALL entities and pages
    ↓
🚀 Execute Promise.all(allRequests) - PARALLEL EXECUTION
    ↓
✅ dispatch(syncContactsFinish()) OR ❌ dispatch(syncContactsFailed())
```

### 3. Completion Phase
```
📡 PUT /api/MobileUserSyncDatabases (sync completion)
    ↓
✅ dispatch(syncCompletionSuccess()) OR ❌ dispatch(syncCompletionFailed())
```

## Supported Entities

The system dynamically syncs **10 contact-related entities**:

| Entity | Key Field | Typical Count | Description |
|--------|-----------|---------------|-------------|
| **Country** | `COU_Guid` | ~251 | Geographic countries |
| **City** | `CIT_Guid` | ~255 | Cities within countries |
| **Classification** | `CLA_Guid` | ~1 | Contact classifications |
| **ContactType** | `CTY_Guid` | ~5 | Types of contacts |
| **ContactingType** | `CTT_Guid` | ~18 | Communication methods |
| **VesselType** | `VST_Guid` | ~11 | Vessel classifications |
| **Contact** | `CNT_Guid` | ~17 | Actual contact records |
| **Address** | `ADR_Guid` | ~7 | Contact addresses |
| **ContactClassification** | `CCL_Guid` | ~7 | Contact-to-classification links |
| **Contacting** | `CTG_Guid` | ~10 | Contact information (emails, phones) |

## Dynamic Pagination

### Configuration
```typescript
const PAGE_SIZE = 1000; // Records per request
```

### Dynamic Page Calculation
```typescript
const totalPages = Math.ceil(entityCount / PAGE_SIZE);

// Example: Country with 2,547 records
// totalPages = Math.ceil(2547 / 1000) = 3 pages
// Creates 3 parallel requests: page 1, 2, 3
```

### Optimizations
- **Zero Count Skip**: Entities with `count: 0` are automatically skipped
- **Parallel Execution**: All pages for all entities run concurrently
- **Smart Loading**: Loading states only complete on final page per entity

## State Management

### Global Sync State (`syncSlice`)
```typescript
interface SyncState {
  // Contacts sync lifecycle
  syncContactsIsLoading: boolean;
  syncContactsErrorText: string | null;
  
  // Completion request
  syncCompletionIsLoading: boolean;
  syncCompletionErrorText: string | null;
  syncContactsDate: string | null;
}
```

### Individual Entity State (`contactsSlice`)
```typescript
interface EntityState {
  byId: Record<string, EntityType>;     // Normalized storage
  allIds: string[];                     // Ordered IDs
}

// Loading states per entity
countriesIsLoading: boolean;
citiesIsLoading: boolean;
// ... etc for all entities
```

## Redux Actions

### Sync Lifecycle Actions
```typescript
// Global sync control
dispatch(syncContactsStart());          // Begin sync
dispatch(syncContactsFinish());         // Success completion
dispatch(syncContactsFailed(error));    // Error handling

// Completion request
dispatch(syncCompletion());             // Start completion
dispatch(syncCompletionSuccess(data));  // Completion success
dispatch(syncCompletionFailed(error));  // Completion error
```

### Entity-Specific Actions
```typescript
// Example for countries (pattern applies to all entities)
dispatch(getCountries());                    // Start loading
dispatch(getCountriesSuccess({ data, isLastPage })); // Add data
dispatch(getCountriesFailed(error));         // Handle error
```

## Data Flow & Processing

### 1. DTO to Internal Type Mapping
```typescript
// Each entity has a dedicated mapper
const mappedCountry = mapCountry(countryDTO);
const mappedCity = mapCity(cityDTO);
// ... etc
```

### 2. Normalized State Updates
```typescript
// Data merging with deduplication
state.countries.byId = { ...state.countries.byId, ...newCountriesById };
state.countries.allIds = Array.from(
  new Set([...state.countries.allIds, ...newIds])
);
```

### 3. Pagination-Aware Loading States
```typescript
// Only set loading to false on final page
if (isLastPage) {
  state.countriesIsLoading = false;
}
```

## Error Handling

### Request-Level Error Handling
```typescript
// Individual requests have built-in error handling
await makeAxiosRequest(
  url,
  () => dispatch(getCountries()),
  (data) => dispatch(getCountriesSuccess({ data, isLastPage })),
  (error) => dispatch(getCountriesFailed(error)), // Per-entity error
  requestConfig
);
```

### Global Sync Error Handling
```typescript
try {
  await Promise.all(allRequests);
  dispatch(syncContactsFinish());
} catch (error) {
  // Any entity failure fails the entire sync
  dispatch(syncContactsFailed(error?.message || 'Sync contacts failed'));
  return; // Skip completion request
}
```

### Error Recovery Strategies
- **Fail Fast**: Any entity failure stops the entire sync
- **State Preservation**: Failed syncs preserve existing data
- **Error Messages**: Detailed error information for debugging
- **Graceful Fallback**: App continues with existing cached data

## API Endpoints

### 1. Get Sync Entity Groups
```http
GET /api/MobileUserSyncEntitiesGroups
Authorization: Bearer {token}
```

### 2. Get Sync Entities List
```http
GET /api/MobileUserSyncEntities?syncEntitiesGroupGuid={guid}
Authorization: Bearer {token}
```

### 3. Sync Entity Data (per entity, per page)
```http
GET /api/MobileUserSyncDatabases?userGuid={userGuid}&dbId={dbId}&entityGuid={entityGuid}&entityName={entityName}&page={page}&pageSize={pageSize}
Authorization: Bearer {token}
```

### 4. Sync Completion
```http
PUT /api/MobileUserSyncDatabases?userGuid={userGuid}&dbId={dbId}&syncEntGroup={syncEntGroupId}
Authorization: Bearer {token}
```

## Performance Characteristics

### Parallel Processing
- **Concurrent Requests**: All entity pages execute simultaneously
- **No Sequential Bottlenecks**: Entities don't wait for each other
- **Optimal Throughput**: Maximum utilization of network resources

### Memory Efficiency
- **Normalized Storage**: `byId` lookup prevents duplicates
- **Incremental Updates**: New data merges with existing state
- **Garbage Collection Friendly**: Minimal object creation

### Network Optimization
- **Large Page Size**: 1,000 records per request reduces round trips
- **Skip Empty Entities**: Zero-count entities don't generate requests
- **Batch Authorization**: Single token for all requests

## Usage Examples

### Triggering Sync Operation
```typescript
import { syncOperation } from '../helpers/syncOperationHelper';

// Dispatch sync operation
dispatch(syncOperation({
  token: authToken,
  domainBaseUrl: 'https://api.example.com',
  userGuid: userGuid,
  dbId: databaseId,
}));
```

### Monitoring Sync Progress
```typescript
// Global sync status
const syncContactsIsLoading = useSelector(state => state.sync.syncContactsIsLoading);
const syncContactsError = useSelector(state => state.sync.syncContactsErrorText);

// Individual entity status
const countriesIsLoading = useSelector(state => state.contacts.countriesIsLoading);
const countries = useSelector(state => state.contacts.countries);
```

### Accessing Synced Data
```typescript
// Get all countries
const allCountries = useSelector(state => {
  return state.contacts.countries.allIds.map(id => 
    state.contacts.countries.byId[id]
  );
});

// Get specific country
const specificCountry = useSelector(state => 
  state.contacts.countries.byId['country-guid-123']
);
```

## Configuration

### Adjustable Parameters
```typescript
// In syncOperationHelper.ts
const PAGE_SIZE = 1000; // Records per page
```

### Entity Action Mapping
```typescript
// In constants/syncEntities.ts
export const SYNC_CONTACTS_ENTITIES_ACTIONS = {
  [SYNC_CONTACTS_ENTITIES_NAMES.country]: {
    getAction: () => getCountries(),
    getSuccessAction: (payload) => getCountriesSuccess(payload),
    getFailedAction: (error) => getCountriesFailed(error),
  },
  // ... additional entities
};
```

## Monitoring & Debugging

### Console Logging
```typescript
// Entity skipping
console.log(`Skipping ${entityName} - no records to sync (count: 0)`);

// Request lifecycle
console.log('Final sync request started');
console.log('Final sync request completed:', data);
console.error('Final sync request failed:', error);
```

### Redux DevTools Integration
- **Action Timeline**: View all dispatched actions in sequence
- **State Inspection**: Monitor entity loading states and data
- **Time Travel**: Debug by replaying sync operations

## Future Enhancements

### Potential Improvements
1. **Delta Sync**: Only sync changed records since last sync
2. **Background Sync**: Sync in background with service workers
3. **Retry Logic**: Automatic retry for failed requests
4. **Compression**: Gzip compression for large responses
5. **Offline Support**: Queue sync operations when offline
6. **Progress Indicators**: Granular progress per entity
7. **Selective Sync**: Allow users to choose which entities to sync

### Architecture Extensibility
- **Plugin System**: Easy addition of new entity types
- **Custom Mappers**: Flexible data transformation pipeline
- **Configurable Endpoints**: Support multiple API versions
- **Event System**: Hooks for sync lifecycle events

## Troubleshooting

### Common Issues

**Sync Stuck in Loading State**
- Check network connectivity
- Verify authentication token validity
- Monitor console for request errors

**Missing Entity Data**
- Confirm entity is enabled on server
- Check entity action mapping in constants
- Verify mapper function exists

**High Memory Usage**
- Consider reducing PAGE_SIZE for large datasets
- Monitor state size in Redux DevTools
- Clear old data before sync if needed

### Debug Steps
1. Enable verbose logging in syncOperationHelper
2. Monitor network requests in browser DevTools
3. Check Redux state changes in Redux DevTools
4. Verify API responses match expected DTO structure

---

## Summary

The Contacts Sync Operation system provides a robust, scalable, and maintainable solution for synchronizing contact-related data. With its dynamic entity processing, dynamic pagination, parallel execution, and comprehensive error handling, it ensures reliable data synchronization while maintaining optimal performance and user experience.

**Key Benefits:**
- ✅ **Dynamic & Extensible**: Automatically handles new entities
- ✅ **High Performance**: Parallel processing with dynamic pagination  
- ✅ **Robust Error Handling**: Comprehensive failure detection and recovery
- ✅ **Normalized State**: Efficient data storage and retrieval
- ✅ **Type Safe**: Full TypeScript integration with proper typing
- ✅ **Production Ready**: Thoroughly tested and optimized architecture
