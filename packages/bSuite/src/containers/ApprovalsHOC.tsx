import React from "react";
import { StyleSheet, View } from "react-native";

// Components
import { Loader } from "bcomponents";
import ApprovalsCategoriesHOC from "./ApprovalsCategoriesHOC";

// Styles
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import { useAuthContext } from "../contexts/AuthContext";

type Props = {};

const ApprovalsHOC = ({}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);
  const { token } = useAuthContext();
  const baseUrl = useAuthContext()?.companyInfo?.bSuiteUrl;

  if (!token && !baseUrl) {
    return (
      <View style={styles.loader}>
        <Loader />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ApprovalsCategoriesHOC />
    </View>
  );
};

export default ApprovalsHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    loader: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
  });

  return { styles, color };
};
