import React, { useEffect, useState } from "react";
import {
  InteractionManager,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation, useIsFocused } from "@react-navigation/native";
import { useQueryClient } from "react-query";
import {
  DynamicViewTaskData,
  setDynamicViewTaskData,
  setLastPerformedActionItem,
} from "../slices/approvalsSlice";
import { queryKeys } from "../queries/queryKeys";
import getEvaluationTask from "../queries/getEvaluationTask";

// Components
import { CustomText, Loader } from "bcomponents";
import TaskComments from "../components/approvalsTaskScreen/taskComments";
import Participants from "../components/approvalsTaskScreen/participants";
import SelectActionModal from "../components/approvalsTaskScreen/selectActionModal";
import ActionModal from "../components/approvalsTaskScreen/actionModal";

// Helpers
import { downloadSingleAttachment } from "bcomponents/downloadHelpers/downloadAttachmentHelper";
import { FILE_DOWNLOAD_STATUS } from "bcomponents/downloadHelpers/downloadFile";
import { useIsForeground } from "../../../bAudit/src/helpers/useIsForeground";

// Styles
import { SPACING } from "bstyles";
import { Theme } from "btheme/ThemeInterface";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";
import BenefitIconSet from "engine/src/assets/fonts/icons";
import { useAuthContext } from "../contexts/AuthContext";

// Define FileDownloadStatus type
type FileDownloadStatus =
  (typeof FILE_DOWNLOAD_STATUS)[keyof typeof FILE_DOWNLOAD_STATUS];

// Configuration for attachment-related keys
// These are the current key names, but the code will try to find alternatives if these exact keys aren't found
const ATTACHMENT_KEYS = {
  // Primary keys (exact matches)
  NAME: "Attachment Name",
  ID: "Attachment Id",

  // Alternative keys that might contain attachment information (partial matches)
  NAME_ALTERNATIVES: [
    "Attachment",
    "File Name",
    "FileName",
    "Document Name",
    "DocumentName",
  ],
  ID_ALTERNATIVES: [
    "Attachment",
    "File ID",
    "FileID",
    "Document ID",
    "DocumentID",
  ],
};

const EvaluationTaskHOC = ({ route }) => {
  const navigation = useNavigation();
  const taskNumber = route?.params?.taskNumber;
  const taskId = route?.params?.taskId;
  const documentType = route?.params?.documentType;
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const isForeground = useIsForeground();
  const isActive = isFocused && isForeground;
  const { styles, color } = useThemeAwareObject(createStyles);
  const { token } = useAuthContext();
  const baseUrl = useAuthContext().companyInfo.bSuiteUrl;

  const { data, isLoading, error, isError, isFetching } = getEvaluationTask({
    usractPndId: taskNumber,
    documentType,
  });

  const dynamicViewTaskData: DynamicViewTaskData = useSelector(
    (state) => state.root.approvalsReducer.dynamicViewTaskData
  );

  const isConnectedToNetwork = useSelector(
    (state) => state.root.approvalsReducer.isConnectedToNetwork
  );

  const [refreshing, setRefreshing] = useState(false);
  const [selecteActionModal, setSelecteActionModal] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedAction, setSelectedAction] = useState<number | null>(null);

  const handleSelectAction = (actionId: number) => {
    setSelectedAction(actionId);
    setSelecteActionModal(false);

    InteractionManager.runAfterInteractions(() => {
      setIsModalVisible(true);
    });
  };

  const handleModalOpen = () => {
    if (!selectedAction) {
      setSelecteActionModal(true);
    } else {
      setIsModalVisible(true);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);

    await queryClient.invalidateQueries(queryKeys.taskInfo);

    setRefreshing(false);
  };

  useEffect(() => {
    if (!isLoading && data) {
      dispatch(setDynamicViewTaskData(data));
    }
  }, [isLoading, data]);

  useEffect(() => {
    navigation.setOptions({
      title: dynamicViewTaskData.vesselName,
    });
  }, [navigation, dynamicViewTaskData]);

  useEffect(() => {
    if (isConnectedToNetwork && isActive) {
      queryClient.invalidateQueries(queryKeys.taskInfo);
    }
  }, [isConnectedToNetwork, isActive]);

  const setLastActionPerformed = ({ id, status }) => {
    dispatch(setLastPerformedActionItem({ id, status }));
  };

  const [downloadStatus, setDownloadStatus] = useState<Record<string, string>>(
    {}
  );

  const handleFilesDownloadStatus = (
    attachmentId: string,
    status: FileDownloadStatus
  ) => {
    setDownloadStatus((prev) => ({
      ...prev,
      [attachmentId]: status,
    }));
  };

  // Helper function to find attachment data regardless of the exact key names
  const findAttachmentData = (content: any[] = []) => {
    // Try to find exact matches first
    let attachmentName = content.find(
      (i) => i?.Key === ATTACHMENT_KEYS.NAME
    )?.Value;
    let attachmentId = content.find(
      (i) => i?.Key === ATTACHMENT_KEYS.ID
    )?.Value;

    // If exact matches aren't found, try alternative keys
    if (!attachmentName) {
      // Look for any key that contains one of the alternative name patterns
      const nameItem = content.find((i) =>
        ATTACHMENT_KEYS.NAME_ALTERNATIVES.some(
          (alt) =>
            i?.Key?.toLowerCase().includes(alt.toLowerCase()) &&
            i?.Key?.toLowerCase().includes("name")
        )
      );
      attachmentName = nameItem?.Value;
    }

    if (!attachmentId) {
      // Look for any key that contains one of the alternative ID patterns
      const idItem = content.find((i) =>
        ATTACHMENT_KEYS.ID_ALTERNATIVES.some(
          (alt) =>
            i?.Key?.toLowerCase().includes(alt.toLowerCase()) &&
            (i?.Key?.toLowerCase().includes("id") ||
              i?.Key?.toLowerCase().includes("guid"))
        )
      );
      attachmentId = idItem?.Value;
    }

    // If we still don't have an attachment name but have an ID, use a default name
    if (!attachmentName && attachmentId) {
      attachmentName = `Attachment-${attachmentId.substring(0, 8)}`;
    }

    return {
      attachmentName,
      attachmentId,
      nameItem: content.find((i) => i?.Value === attachmentName),
      idItem: content.find((i) => i?.Value === attachmentId),
    };
  };

  const handleDownloadAttachment = async (
    attachmentId: string,
    attachmentName: string
  ) => {
    try {
      console.log(`Starting download for: ${attachmentName} (${attachmentId})`);

      await downloadSingleAttachment(
        token.access_token,
        attachmentId,
        attachmentName,
        handleFilesDownloadStatus,
        `${baseUrl}/api/v1.0/attachments-module/Attachments?AttachmentId=${attachmentId}`,
        false
      );
    } catch (error) {
      console.error("Download error:", error);
      handleFilesDownloadStatus(attachmentId, FILE_DOWNLOAD_STATUS.failed);
    }
  };

  // ButtonItem component
  const ButtonItem = ({ item, contentContext = null }) => {
    const handlePress = () => {
      // If we have a content context (parent item's Content array), use it to find attachment data
      if (contentContext) {
        const { attachmentId, attachmentName } =
          findAttachmentData(contentContext);

        if (attachmentId && attachmentName) {
          handleDownloadAttachment(attachmentId, attachmentName);
          return;
        }
      }

      // Fallback to the old way if no context or couldn't find data in context
      if (
        item?.Key?.toLowerCase().includes("attachment") &&
        item?.Key?.toLowerCase().includes("id")
      ) {
        const attachmentId = item?.Value;
        // Try to find the attachment name from various sources
        let attachmentName = data?.Data?.AttachmentName;

        if (!attachmentName && contentContext) {
          // Try to find a name in the content
          const nameKey = contentContext.find(
            (i) =>
              i?.Key?.toLowerCase().includes("attachment") &&
              i?.Key?.toLowerCase().includes("name")
          );
          if (nameKey) {
            attachmentName = nameKey.Value;
          }
        }

        // If still no name, use a default based on ID
        if (!attachmentName && attachmentId) {
          attachmentName = `Attachment-${attachmentId.substring(0, 8)}`;
        }

        if (attachmentId) {
          handleDownloadAttachment(
            attachmentId,
            attachmentName || "attachment"
          );
        } else {
          console.warn("Missing attachment ID for download.");
        }
      } else {
        console.log("Button pressed:", item?.Value);
      }
    };

    // Determine if this is an attachment button
    const isAttachmentButton =
      item?.Key?.toLowerCase().includes("attachment") ||
      (contentContext && findAttachmentData(contentContext).attachmentId);

    return (
      <Pressable
        onPress={handlePress}
        style={{
          backgroundColor: color.BRAND_DEFAULT,
          padding: SPACING.M,
          borderRadius: SPACING.SIX,
          alignItems: "center",
          marginVertical: SPACING.S,
        }}
      >
        <CustomText style={{ fontWeight: "bold", color: "#fff" }}>
          {isAttachmentButton ? "Download" : "Action"}
        </CustomText>
      </Pressable>
    );
  };

  const renderComponent = (item) => {
    switch (item?.Type) {
      case "KeyValue":
        return <KeyValue item={item} />;
      case "Table":
        return <Table item={item} />;
      case "Button":
        return <ButtonItem item={item} />;
      default:
        return <></>;
    }
  };

  const Card = ({ item }) => {
    return (
      <View style={styles.taskDetailsWrapper}>
        <View style={{ flex: 1 }}>
          {item?.Content?.length > 0 &&
            item.Content?.map((item, index) => {
              return (
                <View key={item.Value + `${index}`}>
                  {renderComponent(item)}
                </View>
              );
            })}
        </View>
      </View>
    );
  };

  const KeyValue = ({ item }) => {
    let isHeader = false;

    if (item?.Type === "KeyValue" && item?.Key === "Vessel") {
      isHeader = true;
    }

    return (
      <View
        style={{
          flex: 1,
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {!isHeader && (
          <CustomText style={{ color: color.TEXT_DIMMED, fontSize: 12 }}>
            {item?.Key}
          </CustomText>
        )}

        <CustomText style={{ fontSize: 12 }}>{item?.Value}</CustomText>

        {isHeader && (
          <Pressable
            style={{
              backgroundColor: color.BRAND_DEFAULT,
              padding: SPACING.XS,
              borderRadius: SPACING.SIX,
              marginBottom: SPACING.S,
            }}
            onPress={handleModalOpen}
          >
            <CustomText style={{ fontWeight: "700" }}>Select Action</CustomText>
          </Pressable>
        )}
      </View>
    );
  };

  const Table = ({ item }) => {
    return (
      <View style={{ marginVertical: 5 }}>
        <CustomText style={{ color: color.BRAND_DEFAULT, marginBottom: 5 }}>
          {item?.Title}
        </CustomText>

        {item?.Headers?.length > 0 && item?.Rows?.length ? (
          <ScrollView horizontal>
            {item?.Headers?.map((header, index) => {
              return (
                <View key={header + index} style={{ marginRight: 15 }}>
                  <CustomText style={{ color: color.TEXT_DIMMED }}>
                    {header}
                  </CustomText>

                  <View style={{}}>
                    {item?.Rows?.map((row: string[]) => {
                      const formatValue = (value) => {
                        const numberValue = Number(value);
                        return isNaN(numberValue)
                          ? value
                          : numberValue.toFixed(2);
                      };

                      return (
                        <View key={row[index] + index}>
                          <CustomText style={{}}>
                            {formatValue(row[index])}
                          </CustomText>
                        </View>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </ScrollView>
        ) : (
          <CustomText></CustomText>
        )}
      </View>
    );
  };

  const renderContentItem = (item, index) => {
    switch (item.Type) {
      case "Card": {
        const isComments = item?.Content.find((i) => i?.Key === "Comments");
        if (isComments) {
          return <TaskComments taskComments={isComments?.Value} />;
        }

        // Use our helper function to find attachment data regardless of key names
        const { attachmentId, attachmentName } = findAttachmentData(
          item?.Content || []
        );

        if (attachmentId && attachmentName) {
          return (
            <View style={styles.taskDetailsWrapper}>
              <View style={{ flex: 1 }}>
                <CustomText style={{ marginBottom: SPACING.S }}>
                  {attachmentName}
                </CustomText>
                <ButtonItem
                  item={{ Key: "Attachment Id", Value: attachmentId }}
                  contentContext={item?.Content}
                />
              </View>
            </View>
          );
        }

        return <Card item={item} />;
      }

      case "Table":
        return (
          <View style={styles.taskDetailsWrapper}>
            <Table item={item} />
          </View>
        );

      default:
        return <></>;
    }
  };

  if (isLoading || isFetching) {
    return (
      <View style={styles.container}>
        <Loader />
      </View>
    );
  }

  if (!isConnectedToNetwork) {
    return (
      <View
        style={[
          styles.container,
          {
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
            padding: 50,
          },
        ]}
      >
        <View style={{ flexDirection: "row" }}>
          <BenefitIconSet
            name="x-circle"
            color={color.DESTRUCTIVE_DEFAULT}
            size={24}
            style={{ marginRight: 10 }}
          />
        </View>
        <CustomText>
          Please check your internet connection and try again
        </CustomText>
      </View>
    );
  }

  if (isError) {
    return (
      <View
        style={[
          styles.container,
          {
            flexDirection: "row",
            justifyContent: "center",
            alignItems: "center",
          },
        ]}
      >
        <View style={{ flexDirection: "row" }}>
          <BenefitIconSet
            name="x-circle"
            color={color.DESTRUCTIVE_DEFAULT}
            size={24}
            style={{ marginRight: 10 }}
          />
        </View>
        <CustomText>{error?.message || "An error occurred"}</CustomText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl
            tintColor={color.TEXT_DIMMED}
            colors={[color.TEXT_DIMMED]}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }
        style={{ padding: SPACING.M, marginBottom: SPACING.M }}
      >
        {data?.Content?.map((item, index) => (
          <View key={item.Type + index}>{renderContentItem(item, index)}</View>
        ))}

        <Participants documentId={taskId} documentType={documentType} />
      </ScrollView>

      <SelectActionModal
        isVisible={selecteActionModal}
        setIsVisible={setSelecteActionModal}
        onSelectAction={handleSelectAction}
        taskData={dynamicViewTaskData}
      />

      <ActionModal
        isVisible={isModalVisible}
        setIsVisible={setIsModalVisible}
        taskId={taskNumber}
        selectedAction={selectedAction}
        setSelectedAction={setSelectedAction}
        canAddParticipant={dynamicViewTaskData.canAddParticipant || 0}
        setLastActionPerformedItem={setLastActionPerformed}
      />
    </View>
  );
};

export default EvaluationTaskHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    taskDetailsWrapper: {
      backgroundColor: color.PRESSABLE,
      padding: SPACING.M,
      borderRadius: SPACING.SIX,
      marginBottom: SPACING.M,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
  });

  return { styles, color };
};
