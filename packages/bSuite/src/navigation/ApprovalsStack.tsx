import React from "react";
import { Platform, View } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { getHeaderTitle } from "@react-navigation/elements";
import { routeNames } from "./routeNames";

// Components
import { NavigationHeader, IconButton } from "bcomponents";
import FilterScreen from "../components/FiltersScreen";
import ApprovalsHOC from "../containers/ApprovalsHOC";
import SingleCategoryHOC from "../containers/SingleCategoryHOC";
import ApprovalTaskHOC from "../containers/ApprovalTaskHOC";
import EvaluationTaskHOC from "../containers/EvaluationTaskHOC";

// Styles
import { SPACING } from "bstyles";
import { useThemeAwareObject } from "btheme/useThemeAwareObjects";

const Stack = createNativeStackNavigator();

const ApprovalsStack = ({}) => {
  return (
    <Stack.Navigator
      initialRouteName={routeNames.myTasks}
      screenOptions={{
        headerBackTitleVisible: false,
        header: ({ navigation, route, options, back }) => {
          const title = getHeaderTitle(options, route.name);
          const { color } = useThemeAwareObject((color) => color);
          const isSummary = route.name === routeNames.myTasks;

          const onFilterPress = () => {
            navigation.navigate({
              name: routeNames.filterScreen,
              params: {
                isSummary: isSummary,
              },
            });
          };

          return (
            <NavigationHeader
              title={title}
              navigation={navigation}
              route={route}
              backButton={!!back}
              onPress={() => navigation.goBack()}
              paddingHorizontal={20}
            >
              <View style={{ flexDirection: "row" }}>
                <IconButton
                  iconName={"filter"}
                  style={{
                    paddingVertical: 14,
                    paddingHorizontal: 14,
                  }}
                  iconColor={color.BRAND_DEFAULT}
                  onPress={onFilterPress}
                />
              </View>
            </NavigationHeader>
          );
        },
        presentation: "card",
        animationTypeForReplace: "push",
        animation: Platform.OS === "ios" ? "none" : "slide_from_right",
      }}
    >
      <Stack.Screen name={routeNames.myTasks} component={ApprovalsHOC} />

      <Stack.Screen
        name={routeNames.filterScreen}
        component={FilterScreen}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <NavigationHeader
                title={title}
                navigation={navigation}
                route={route}
                backButton={!!back}
                onPress={() => navigation.goBack()}
                paddingHorizontal={20}
              />
            );
          },
        }}
      />
      <Stack.Screen
        name={routeNames.singleCategoryList}
        component={SingleCategoryHOC}
      />
      <Stack.Screen
        name={routeNames.ApprovalTaskHOC}
        component={ApprovalTaskHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <NavigationHeader
                title={title}
                navigation={navigation}
                route={route}
                backButton={!!back}
                onPress={() => navigation.goBack()}
                paddingHorizontal={20}
              />
            );
          },
        }}
      />

      <Stack.Screen
        name={routeNames.EvaluationTaskHOC}
        component={EvaluationTaskHOC}
        options={{
          header: ({ navigation, route, options, back }) => {
            const title = getHeaderTitle(options, route.name);

            return (
              <NavigationHeader
                title={title}
                navigation={navigation}
                route={route}
                backButton={!!back}
                onPress={() => navigation.goBack()}
                paddingHorizontal={20}
              />
            );
          },
        }}
      />
    </Stack.Navigator>
  );
};

export default ApprovalsStack;
