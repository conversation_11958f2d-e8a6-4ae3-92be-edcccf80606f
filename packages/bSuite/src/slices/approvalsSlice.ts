import { createSlice } from "@reduxjs/toolkit";

type DocType = {
  ID: number;
  DocType: string;
};


type Approval = {
  id: number;
  categoryId: number;
  category: string;
  vesselId: number;
  vesselName: string;
  number: string;
  importance: 0 | 1;
};

type categoryApproval = {
  documentId: string;
  taskNumber: number;
  categoryId: number;
  amount: number;
  supplier: string;
  importance: 0 | 1;
  vesselId: number;
  expensesIds: Array<number>;
  status: 0 | 1 | 2 | 3 | 4 | 5 | 6 | null
};

type Vessel = {
  id: number;
  vesselName: string;
};

type Fleet = {
  id: number;
  fleetName: string;
};

type Category = {
  id: number;
  categoryName: string;
};

type Sender = {
  id: number;
  senderName: string;
};

type Expense = {
  id: number;
  expenseName: string;
};

type Responsible = {
  id: number;
  responsible: string;
}

type Filters = {
  fleetId: string;
  vesselId: string;
  categoryId: string;
  senderId: string;
  expenseId: string;
};

export type Content = {
  Content: Card[];
  Data: {
    ID: number
    VSL_ID: number
    VSL_NAME: string
    DOC_DATE: Date
    DOC_REF: string
    DOC_CUR: string
    DOC_CATEG: string
    EVAL_PAYABLES: string
    TOTAL_COST: string | null
    FL_NEXT_PART: 1 | 0 | null
    FL_TYPE: 1 | 2
    FL_RESPONS: 0 | 1 | 2 | 3 | 4 | 5 | 6 | null
    PND_NOTES: string
    Case: string
    Notes: string
    AttachmentId: string
    AttachmentName: string
  }
}

type Card = {
  Content: (Card | CardContent)[];
  Type: "Card";
};

type CardContent = (KeyValueItem | TableItem)[];

type KeyValueItem = {
  Value: string;
  Key: string;
  Type: "KeyValue";
};

type TableRow = string[];

type TableItem = {
  Title: string;
  Headers: string[];
  Rows: TableRow[];
  Type: "Table";
};
export type TaskInfo = {
  vesselId: number | null;
  vesselName: string;
  expenseDescriptions: string;
  expenseIds: string[];
  supplier: string;
  amount: number | null;
  taskId: number | null;
  taskRef: string;
  taskDate: Date;
  taskNotes: string;
  orderDate: Date;
  orderAmount: number | null;
  canAddParticipant: 1 | 0 | null;
  canApprove: 1 | 2 | null; // 1: Review, 2: Approve/Reject
  taskComments: string;
  taskStatus: 0 | 1 | 2 | 3 | 4 | 5 | 6 | null; // Status του Task (0: Pending, 1: Accepted/Replied, 2: Rejected, 3: Cancelled, 4: Waiting – Ask for Clarifications, 5: Returned, 6: Redirected)
};

type Status = {
  name: string;
  iconName: string;
  color: string;
};

export type DynamicViewTaskData = {
  content: Content | null;
  vesselId: number | null;
  vesselName: string;
  expenseDescriptions: string;
  expenseIds: string[];
  supplier: string;
  amount: number | null;
  taskId: number | null;
  taskRef: string;
  taskDate: Date;
  taskNotes: string;
  orderDate: Date;
  orderAmount: number | null;
  canAddParticipant: 1 | 0 | null;
  canApprove: 1 | 2 | null; // 1: Review, 2: Approve/Reject
  taskComments: string;
  taskStatus: 0 | 1 | 2 | 3 | 4 | 5 | 6 | null; // Status του Task (0: Pending, 1: Accepted/Replied, 2: Rejected, 3: Cancelled, 4: Waiting – Ask for Clarifications, 5: Returned, 6: Redirected)
};

type NormalizedApproval = {
  [id: string]: Approval | string[];
};
type NormalizedCategoryApproval = {
  [id: string]: categoryApproval | string[];
};

type NormalizedVessel = {
  [id: string]: Vessel | string[];
};

type NormalizedFleet = {
  [id: string]: Fleet | string[];
};

type NormalizedCategory = {
  [id: string]: Category | string[];
};

type NormalizedSender = {
  [id: string]: Sender | string[];
};

type NormalizedResponsible = {
  [id: string]: Responsible | string[];
};

type NormalizedExpense = {
  [id: string]: Expense | string[];
};

type NormalizedStatuses = {
  [id: string]: Status | string[];
};

type State = {
  baseUrl: string;
  approvals: NormalizedApproval;
  approvalsListCategory: NormalizedCategoryApproval;
  vessels: NormalizedVessel;
  fleets: NormalizedFleet;
  categories: NormalizedCategory;
  senders: NormalizedSender;
  responsibles: NormalizedResponsible;
  expenses: NormalizedExpense;
  approvalsFilters: Filters;
  approvalsCategoryFilters: Filters;
  token: {
    access_token: string;
  };
  searchTerm: string;
  taskData: TaskInfo;
  taskStatusMapper: NormalizedStatuses;
  hasChanged: boolean;
  groupVessel: boolean;
  isConnectedToNetwork: boolean;
  scrollIndex: number | null;
  docTypes: {
    [id: string]: DocType;
  };
  dynamicViewTaskData: DynamicViewTaskData;
  lastPerformedActionItem: categoryApproval | null;
};

const initialState: State = {
  approvals: { allIds: [] },
  approvalsListCategory: { allIds: [] },
  vessels: { allIds: [] },
  fleets: { allIds: [] },
  categories: { allIds: [] },
  senders: { allIds: [] },
  responsibles: { allIds: [] },
  expenses: { allIds: [] },
  approvalsFilters: {
    fleetId: "",
    categoryId: "",
    vesselId: "",
    senderId: "",
    expenseId: "",
  },
  approvalsCategoryFilters: {
    fleetId: "",
    categoryId: "",
    vesselId: "",
    senderId: "",
    expenseId: "",
  },
  baseUrl: "",
  token: {
    access_token: "",
  },
  searchTerm: "",
  taskData: {
    vesselId: null,
    vesselName: '',
    expenseDescriptions: '',
    expenseIds: [],
    supplier: '',
    amount: null,
    taskId: null,
    taskRef: '',
    taskDate: new Date(),
    taskNotes: '',
    orderDate: new Date(),
    orderAmount: null,
    canAddParticipant: null,
    canApprove: null,
    taskComments: '',
    taskStatus: null,
  },
  taskStatusMapper: { allIds: [] },
  hasChanged: false,
  groupVessel: false,
  isConnectedToNetwork: false,
  scrollIndex: null,
  docTypes: {
    "50": {
      ID: 50,
      DocType: "Order",
    },
    "70": {
      ID: 70,
      DocType: "Invoice",
    },
    "40": {
      ID: 40,
      DocType: "Evaluation Report",
    },
  },
  dynamicViewTaskData: {
    content: [],
    vesselId: null,
    vesselName: '',
    expenseDescriptions: '',
    expenseIds: [],
    supplier: '',
    amount: null,
    taskId: null,
    taskRef: '',
    taskDate: new Date(),
    taskNotes: '',
    orderDate: new Date(),
    orderAmount: null,
    canAddParticipant: null,
    canApprove: null,
    taskComments: '',
    taskStatus: null,
  },
  lastPerformedActionItem: null,
};

const approvalsSlice = createSlice({
  name: "approvals",
  initialState,
  reducers: {
    setToken(state, { payload }) {
      state.token = payload;
    },
    setApprovals(state, { payload }) {
      console.log('payload', payload)

      try {
        state.approvals.allIds = [];
        // state.vessels.allIds = [];
        payload?.map((approval) => {
          const vesselId = approval.VSL_ID ? approval.VSL_ID : "All Vessels";
          const vesselName = approval.VSL_NAME ? approval.VSL_NAME : "All Vessels";
          // filtering only for Order and Invoice categories.

          if (approval.VSL_ID)
            state.vessels = {
              ...state.vessels,
              [approval.VSL_ID]: {
                id: approval.VSL_ID,
                vesselName: approval.VSL_NAME,
              },
              allIds:
                state.vessels.allIds.indexOf(approval.VSL_ID) === -1
                  ? [...state.vessels.allIds, approval.VSL_ID]
                  : state.vessels.allIds,
            };

          state.categories = {
            ...state.categories,
            [approval.Category_ID]: {
              id: approval.Category_ID,
              categoryName: approval.Category,
            },

            allIds: state.categories.allIds.indexOf(approval?.Category_ID) === -1
              ? [...state.categories.allIds, approval?.Category_ID]
              : state.categories.allIds,
          };

          state.approvals = {
            ...state.approvals,
            [approval.ID]: {
              id: approval.ID,
              categoryId: approval.Category_ID,
              category: approval.Category,
              vesselId: approval.VSL_ID,
              number: approval.Number,
              importance: approval.Importance,
            },
            allIds:
              state.approvals.allIds.indexOf(approval.ID) === -1
                ? [...state.approvals.allIds, approval.ID]
                : state.approvals.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setApprovals: ", error);
      }
    },
    setCategoryApprovals(state, { payload }) {
      try {
        state.approvalsListCategory.allIds = [];
        payload?.map((task) => {

          state.vessels = {
            ...state.vessels,
            [task.VSL_ID]: {
              id: task.VSL_ID,
              vesselName: task.VSL_NAME,
            },
            allIds:
              state.vessels.allIds.indexOf(task.VSL_ID) === -1
                ? [...state.vessels.allIds, task.VSL_ID]
                : state.vessels.allIds,
          };

          state.approvalsListCategory = {
            ...state.approvalsListCategory,
            [task?.USRACT_PND_ID]: {
              vesselName: task?.VSL_NAME,
              documentId: task?.DOC_ID,
              taskNumber: task?.USRACT_PND_ID,
              categoryId: task?.DOC_TP,
              amount: task?.AMOUNT,
              supplier: task?.SUPPLIER,
              importance: task?.Importance,
              vesselId: task?.VSL_ID,
              expensesIds: task?.BGACC_IDS?.split(",")?.slice(1)?.map(Number),
              status: task?.FL_RESPONS,
              docRef: task?.DOC_REF,
            },
            allIds:
              state.approvalsListCategory.allIds.indexOf(
                task?.USRACT_PND_ID
              ) === -1
                ? [...state.approvalsListCategory.allIds, task?.USRACT_PND_ID]
                : state.approvalsListCategory.allIds,
          };

          const taskExpenses = task?.BGACC_IDS?.split(",");
          state.approvalsListCategory[task?.USRACT_PND_ID]?.expensesIds?.map(
            (expenseId, index) => {
              state.expenses = {
                ...state.expenses,
                [expenseId]: {
                  id: expenseId,
                  expenseName: state.expenses[expenseId]?.expenseName,
                },
              };
            }
          );
        });
      } catch (error) {
        console.error("Error in setCategoryApprovals: ", error);
      }
    },
    setFleets(state, { payload }) {
      try {
        payload?.map((fleet) => {
          state.fleets = {
            ...state.fleets,
            [fleet?.ID]: {
              fleetId: fleet?.ID,
              fleetName: fleet?.VesselGroup,
            },
            allIds:
              state.fleets.allIds.indexOf(fleet?.ID) === -1
                ? [...state.fleets.allIds, fleet?.ID]
                : state.fleets.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setFleets: ", error);
      }
    },
    setVessels(state, { payload }) {
      state.vessels.allIds = [];

      try {
        payload?.map((vessel) => {
          state.vessels = {
            ...state.vessels,
            [vessel?.ID]: {
              vesselId: vessel?.ID,
              vesselName: vessel?.Vessel,
            },
            allIds:
              state.vessels.allIds.indexOf(vessel?.ID) === -1
                ? [...state.vessels.allIds, vessel?.ID]
                : state.vessels.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setVessels: ", error);
      }
    },
    setSenders(state, { payload }) {
      try {
        payload?.map((sender) => {
          state.senders = {
            ...state.senders,
            [sender?.ID]: {
              senderId: sender?.ID,
              senderName: sender?.Sender,
            },
            allIds:
              state.senders.allIds.indexOf(sender?.ID) === -1
                ? [...state.senders.allIds, sender?.ID]
                : state.senders.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setSenders: ", error);
      }
    },
    setResponsibles(state, { payload }) {
      try {
        payload?.map((responsible) => {
          state.responsibles = {
            ...state.responsibles,
            [responsible?.ID]: {
              id: responsible?.ID,
              responsible: responsible?.DESCR,
            },
            allIds:
              state.responsibles.allIds.indexOf(responsible?.ID) === -1
                ? [...state.responsibles.allIds, responsible?.ID]
                : state.responsibles.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setResponsibles: ", error);
      }
    },
    setExpenses(state, { payload }) {
      try {
        payload.map((expense) => {
          state.expenses = {
            ...state.expenses,
            [expense?.ID]: {
              expenseId: expense?.ID,
              expenseName: expense?.Expense,
            },
            allIds:
              state.expenses.allIds.indexOf(expense?.ID) === -1
                ? [...state.expenses.allIds, expense?.ID]
                : state.expenses.allIds,
          };
        });
      } catch (error) {
        console.error("Error in setExpenses: ", error);
      }
    },
    setApprovalsListFilters(state, { payload }) {
      try {
        state.approvalsFilters.fleetId = payload.fleetId;
        state.approvalsFilters.vesselId = payload.vesselId;
        state.approvalsFilters.categoryId = payload.categoryId;
        state.approvalsFilters.senderId = payload.senderId;
        state.approvalsFilters.expenseId = payload.expenseId;
      } catch (error) {
        console.error("Error in setApprovalsListFilters: ", error);
      }
    },
    setApprovalsCategoryListFilters(state, { payload }) {
      try {
        state.approvalsCategoryFilters.vesselId = payload.vesselId;
        state.approvalsCategoryFilters.fleetId = payload.fleetId;
        state.approvalsCategoryFilters.categoryId = payload.categoryId;
        state.approvalsCategoryFilters.senderId = payload.senderId;
        state.approvalsCategoryFilters.expenseId = payload.expenseId;
      } catch (error) {
        console.error("Error in setApprovalsCategoryListFilters: ", error);
      }
    },
    setSearchTerm(state, { payload }) {
      state.searchTerm = payload;
    },
    setTaskData(state, { payload }) {
      const data = payload[0]

      state.taskData = {
        vesselId: data.VSL_ID,
        vesselName: data.VSL_NAME,
        expenseDescriptions: data.CBGACC_IDS,
        expenseIds: data.BGACC_IDS,
        supplier: data.ADR_NAME,
        orderDate: data.ORD_DATE,
        orderAmount: data.ORD_AMNT,
        canAddParticipant: data.FL_NEXT_PART,
        canApprove: data.FL_TYPE,
        taskComments: data.PND_NOTES || data.ORD_NOTES,
        taskStatus: data.FL_RESPONS,
        amount: data.AMOUNT || data.ORD_AMNT,
        taskId: data.SLINV_ID || data.SLORD_ID,
        taskNotes: data.INV_NOTES || data.ORD_NOTES,
        taskRef: data.INV_REF || data.ORD_REF,
        taskDate: data.INV_DATE || data.ORD_DATE,
      }
    },
    setDynamicViewTaskData(state, { payload }: { payload: Content }) {
      state.dynamicViewTaskData = {
        content: payload.Content,
        vesselId: payload.Data.VSL_ID,
        vesselName: payload.Data.VSL_NAME,
        expenseDescriptions: payload.Data.DOC_CATEG,
        expenseIds: payload.Data.EVAL_PAYABLES.split(","),
        supplier: payload.Data.ADR_NAME,
        amount: payload.Data.TOTAL_COST,
        taskId: payload.Data.ID,
        taskRef: payload.Data.DOC_REF,
        taskDate: payload.Data.DOC_DATE,
        taskNotes: payload.Data.PND_NOTES,
        orderDate: payload.Data.DOC_DATE,
        orderAmount: payload.Data.TOTAL_COST,
        canAddParticipant: payload.Data.FL_NEXT_PART,
        canApprove: payload.Data.FL_TYPE,
        taskComments: payload.Data.Notes,
        taskStatus: payload.Data.FL_RESPONS,
      }
    },
    setStatusMapper(state, { payload }) {
      state.taskStatusMapper = payload;
    },
    setHasChanged(state, { payload }) {
      state.hasChanged = payload;
    },
    setBaseUrl(state, { payload }) {
      state.baseUrl = payload;
    },
    setGroupVessel(state, { payload }) {
      state.groupVessel = payload;
    },
    setIsConnectedToNetwork(state, action) {
      state.isConnectedToNetwork = action.payload;
    },
    setScrollIndex(state, action) {
      state.scrollIndex = action.payload;
    },
    setLastPerformedActionItem: (state, { payload }) => {
      state.lastPerformedActionItem = {
        ...state.approvalsListCategory[payload.id],
        status: 1
      }
    },
    clearLastPerformedActionItem: (state, action) => {
      state.lastPerformedActionItem = null
    },
  }
});

export const {
  setToken,
  setBaseUrl,
  setApprovals,
  setFleets,
  setVessels,
  setSenders,
  setResponsibles,
  setExpenses,
  setSearchTerm,
  setTaskData,
  setStatusMapper,
  setHasChanged,
  setApprovalsCategoryListFilters,
  setApprovalsListFilters,
  setCategoryApprovals,
  setGroupVessel,
  setIsConnectedToNetwork,
  setScrollIndex,
  setDynamicViewTaskData,
  setLastPerformedActionItem,
  clearLastPerformedActionItem
} = approvalsSlice.actions;

export default approvalsSlice.reducer;
