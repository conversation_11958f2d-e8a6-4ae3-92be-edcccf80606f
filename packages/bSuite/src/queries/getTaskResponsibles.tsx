import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

type Responsible = {
  id: number;
  responsible: string;
};

const getTaskResponsibles = () => {
  const { makeAxiosCall } = useAuthContext();

  const fetchTaskResponsibles = async () => {
    const data = await makeAxiosCall(
      `approvals-module/responsible-users/lookup`
    );

    return data;
  };

  const { data, error, isError, isLoading } = useQuery<Responsible[] | null>(
    queryKeys.responsibles,
    fetchTaskResponsibles
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getTaskResponsibles;
