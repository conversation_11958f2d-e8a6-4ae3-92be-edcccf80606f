import axios from "axios";

const putApprovalAction = async (
  baseUrl,
  token,
  taskId,
  selectedAction,
  comment,
  redirectIds
) => {
  try {
    const body = {
      URESP_IDS: "",
      FL_PNDTP: "2",
      FL_RESPONS: selectedAction,
      RESPONSE_NOTES: comment,
    };

    redirectIds &&
      redirectIds.length > 0 &&
      (body.URESP_IDS = redirectIds.join(","));

    const { data } = await axios.patch(
      `${baseUrl}/api/v1.0/approvals-module/tasks/${taskId}/approvals`,
      body,
      {
        headers: { authorization: `Bearer ${token?.access_token}` },
      }
    );

    return data;
  } catch (error) {
    console.error(`An error occurred while fetching user flow: ${error}`);
    throw error;
  }
};

export default putApprovalAction;
