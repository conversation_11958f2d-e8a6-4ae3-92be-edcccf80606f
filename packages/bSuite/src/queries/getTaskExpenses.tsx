import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

type Expense = {
  id: number;
  invoiceId: number;
  invoiceRef: string;
  expenseId: number;
  expenseName: string;
  budget: number;
  cost: number;
  budgetPerYearToDate: number;
};

const getTaskExpenses = (usractPndId) => {
  const { makeAxiosCall } = useAuthContext();

  const fetchTaskExpenses = async () => {
    const data = await makeAxiosCall(
      `approvals-module/tasks/${usractPndId}/cost-analysis`
    );

    let newData = [];

    data &&
      data.length > 0 &&
      data.map((item) => {
        newData.push({
          id: item.ID,
          invoiceId: item.SLINV_ID,
          invoiceRef: item.INV_REF,
          expenseId: item.BGACC_ID,
          expenseName: item.BG_ACC_NAME,
          budget: item.BUDGET,
          cost: item.COST,
          budgetPerYearToDate: item.BUDGET_YTD,
        });
      });

    return newData;
  };

  const { data, error, isError, isLoading } = useQuery<Expense[] | null>(
    queryKeys.taskExpenses({ usractPndId }),
    fetchTaskExpenses,
    {
      enabled: !!usractPndId,
    }
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getTaskExpenses;
