import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

const getExpenses = () => {
  const { makeAxiosCall } = useAuthContext();

  const fetchExpenses = async () => {
    const data = await makeAxiosCall(
      `approvals-module/expenses/lookup?hasUserPendingTasks=true`
    );

    return data;
  };

  const { data, error, isError, isLoading } = useQuery(
    queryKeys.expenses,
    fetchExpenses
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getExpenses;
