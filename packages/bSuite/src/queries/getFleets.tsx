import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

const getFleets = () => {
  const { makeAxiosCall } = useAuthContext();

  const fetchBaseUrl = async () => {
    const data = await makeAxiosCall(
      `approvals-module/fleets/lookup?hasUserPendingTasks=true`
    );

    return data;
  };

  const { data, error, isError, isLoading } = useQuery(
    queryKeys.fleets,
    fetchBaseUrl
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getFleets;
