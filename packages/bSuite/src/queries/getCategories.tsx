import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

const getCategories = (
  fleetId,
  vesselId,
  categoryId,
  senderId,
  expenseId,
  groupVessel
) => {
  const { makeAxiosCall } = useAuthContext();

  const enabledFilters = {
    hasUserPendingTasks: true,
    groupVessel,
    VSL_GRP_ID: fleetId,
    VSL_ID: vesselId,
    UPDOC_TYPE_ID: categoryId,
    SENDER_ID: senderId,
    BGACC_ID: expenseId,
  };

  const queryParams = Object.entries(enabledFilters)
    .map((e) => e.join("="))
    .join("&");

  const fetchBaseUrl = async () => {
    const data = await makeAxiosCall(
      `approvals-module/categories?${queryParams}`
    );

    return data;
  };

  const { data, error, isError, isLoading, isFetching } = useQuery(
    queryKeys.categories({
      fleetId,
      vesselId,
      categoryId,
      senderId,
      expenseId,
      groupVessel,
    }),
    fetchBaseUrl
  );

  return {
    data,
    error,
    isError,
    isLoading,
    isFetching,
  };
};

export default getCategories;
