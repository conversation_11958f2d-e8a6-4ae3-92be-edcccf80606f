import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";

import { useAuthContext } from "../contexts/AuthContext";

const getVessels = (fleetId) => {
  const { makeAxiosCall } = useAuthContext();

  const fetchVessels = async () => {
    const data = await makeAxiosCall(
      `approvals-module/vessels/lookup?hasUserPendingTasks=true&vsl_grp_id=${fleetId}`
    );

    return data;
  };

  const { data, error, isError, isLoading } = useQuery(
    queryKeys.vessels({ fleetId }),
    fetchVessels
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getVessels;
