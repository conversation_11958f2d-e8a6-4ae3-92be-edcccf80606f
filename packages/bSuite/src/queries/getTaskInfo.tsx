import axios from "axios";
import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useSelector } from "react-redux";
import { TaskInfo } from "../slices/approvalsSlice";
import { useAuthContext } from "../contexts/AuthContext";

const getTaskInfo = ({ usractPndId, documentType }) => {
  const { makeAxiosCall } = useAuthContext();

  const type = documentType === 70 ? "invoice" : "order-info";
  const fetchTaskInfo = async () => {
    const data = await makeAxiosCall(
      `approvals-module/tasks/${usractPndId}/${type}`
    );

    return data;
  };

  const { data, error, isError, isLoading, isFetching } = useQuery<TaskInfo>(
    queryKeys.taskInfo({ usractPndId, documentType }),
    fetchTaskInfo,
    {
      enabled: !!usractPndId,
    }
  );

  return {
    data,
    error,
    isError,
    isLoading,
    isFetching,
  };
};

export default getTaskInfo;
