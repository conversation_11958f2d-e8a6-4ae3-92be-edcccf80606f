import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { Content } from "../slices/approvalsSlice";
import { useAuthContext } from "../contexts/AuthContext";

const getEvaluationTask = ({ usractPndId, documentType }) => {
  const { token } = useAuthContext();
  const baseUrl = useAuthContext().companyInfo.bSuiteUrl;
  const { makeAxiosCall } = useAuthContext();

  const fetchTaskInfo = async () => {
    const data = await makeAxiosCall(
      `approvals-module/tasks/${usractPndId}/evaluation`
    );

    return data;
  };

  const { data, error, isError, isLoading, isFetching } = useQuery<Content>(
    queryKeys.taskInfo({ usractPndId, documentType }),
    fetchTaskInfo,
    {
      enabled: !!usractPndId,
    }
  );

  return {
    data,
    error,
    isError,
    isLoading,
    isFetching,
  };
};

export default getEvaluationTask;
