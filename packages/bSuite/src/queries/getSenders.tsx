import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

const getSenders = () => {
  const { makeAxiosCall } = useAuthContext();

  const fetchSenders = async () => {
    const data = await makeAxiosCall(
      `approvals-module/senders/lookup?hasUserPendingTasks=true`
    );

    return data;
  };

  const { data, error, isError, isLoading } = useQuery(
    queryKeys.senders,
    fetchSenders
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getSenders;
