import { useQuery } from "react-query";
import { queryKeys } from "./queryKeys";
import { useAuthContext } from "../contexts/AuthContext";

type Participant = {
  id: number;
  responsible: string;
  nodeLevel: number;
  action: string;
  userId: number;
  result: string;
  notes: string;
  userName: string;
};

const getTaskParticipants = ({ documentId, documentType }) => {
  const { makeAxiosCall } = useAuthContext();

  const fetchTaskParticipants = async () => {
    const data = await makeAxiosCall(
      `approvals-module/documents/${documentId}/tasks?documentType=${documentType}`
    );

    let newData = [];

    data &&
      data.length > 0 &&
      data.map((item) => {
        newData.push({
          id: item.ID,
          responsible: item.RESPONSIBLE,
          nodeLevel: item.NODE_LEVEL,
          action: item.ACTION,
          userId: item.DONE_USER_ID,
          result: item.RESULT,
          notes: item.DONE_NOTES,
          userName: item.DONE_USER_NAME,
        });
      });

    return newData;
  };

  const { data, error, isError, isLoading } = useQuery<Participant[] | null>(
    queryKeys.taskParticipants({ documentId, documentType }),
    fetchTaskParticipants,
    {
      enabled: !!documentId,
    }
  );

  return {
    data,
    error,
    isError,
    isLoading,
  };
};

export default getTaskParticipants;
